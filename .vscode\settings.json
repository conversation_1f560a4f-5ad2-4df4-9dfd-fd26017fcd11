{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "editor.formatOnSave": false, "eslint.validate": ["html", "css", "scss", "json", "jsonc"], "i18n-ally.displayLanguage": "zh-cn", "i18n-ally.enabledParsers": ["ts"], "i18n-ally.enabledFrameworks": ["vue"], "i18n-ally.editor.preferEditor": true, "i18n-ally.keystyle": "nested", "i18n-ally.localesPaths": ["src/locales/langs"], "i18n-ally.parsers.typescript.compilerOptions": {"moduleResolution": "node"}, "prettier.enable": false, "typescript.tsdk": "node_modules/typescript/lib", "unocss.root": ["./"], "vue-i18n.i18nPaths": "src\\locales,src\\locales\\langs,packages\\scripts\\src\\locales"}