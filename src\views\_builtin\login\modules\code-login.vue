<script setup lang="ts">
import { computed, reactive } from 'vue';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { fetchSmsLogin } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';

defineOptions({
  name: 'CodeLogin'
});

const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();

interface FormModel {
  phone: string;
  code: string;
  username: string;
}

const model: FormModel = reactive({
  phone: '',
  code: '',
  username: ''
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules } = useFormRules();

  return {
    phone: formRules.phone,
    code: formRules.code,
    username: formRules.userName
  };
});

async function handleSubmit() {
  await validate();

  try {
    const smsLoginParams: Api.Auth.SmsLoginParams = {
      phone: model.phone,
      code: model.code,
      username: model.username
    };

    const { data: loginToken, error } = await fetchSmsLogin(smsLoginParams);

    if (!error && loginToken) {
      // 使用认证store的loginByToken方法处理登录
      const pass = await authStore.loginByToken(loginToken);

      if (pass) {
        window.$message?.success($t('page.login.common.loginSuccess'));
        // 登录成功后的重定向等逻辑会在loginByToken中处理
        // 这里可以添加额外的成功处理逻辑
      } else {
        window.$message?.error($t('page.login.codeLogin.loginFailed'));
      }
    } else {
      window.$message?.error($t('page.login.codeLogin.loginFailed'));
    }
  } catch (err) {
    window.$message?.error($t('page.login.codeLogin.loginFailed'));
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <NFormItem path="phone">
      <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.code" :placeholder="$t('page.login.common.codePlaceholder')" />
        <NButton size="large" :disabled="isCounting" :loading="loading" @click="getCaptcha(model.phone)">
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <!-- <NFormItem path="username">
      <NInput v-model:value="model.username" :placeholder="$t('page.login.common.userNamePlaceholder')" />
    </NFormItem> -->
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
