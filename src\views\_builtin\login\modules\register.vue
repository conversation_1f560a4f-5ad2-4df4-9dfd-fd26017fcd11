<script setup lang="ts">
import { computed, reactive } from 'vue';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { fetchRegister } from '@/service/api';
import { $t } from '@/locales';

defineOptions({
  name: 'Register'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();

interface FormModel {
  phone: string;
  code: string;
  username: string;
  password: string;
  confirmPassword: string;
  role: string;
}

const model: FormModel = reactive({
  phone: '',
  code: '',
  username: '',
  password: '',
  confirmPassword: '',
  role: 'admin'
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules, createConfirmPwdRule } = useFormRules();

  return {
    phone: formRules.phone,
    code: formRules.code,
    username: formRules.userName,
    password: formRules.pwd,
    confirmPassword: createConfirmPwdRule(model.password),
    role: [{ required: true, message: $t('form.role.required'), trigger: 'change' }]
  };
});

async function handleSubmit() {
  await validate();

  try {
    const registerParams: Api.Auth.RegisterParams = {
      phone: model.phone,
      username: model.username,
      password: model.password,
      role: model.role
    };

    const { data, error } = await fetchRegister(registerParams);

    if (!error && data?.success) {
      window.$message?.success($t('page.login.register.success'));
      // 注册成功后跳转到登录页面
      toggleLoginModule('pwd-login');
    } else {
      window.$message?.error(data?.message || $t('page.login.register.failed'));
    }
  } catch (err) {
    window.$message?.error($t('page.login.register.failed'));
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <NFormItem path="phone">
      <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.code" :placeholder="$t('page.login.common.codePlaceholder')" />
        <NButton size="large" :disabled="isCounting" :loading="loading" @click="getCaptcha(model.phone)">
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NFormItem path="username">
      <NInput v-model:value="model.username" :placeholder="$t('page.login.common.userNamePlaceholder')" />
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.confirmPasswordPlaceholder')"
      />
    </NFormItem>
    <NFormItem path="role">
      <NSelect
        v-model:value="model.role"
        :placeholder="$t('page.login.register.rolePlaceholder')"
        :options="[
          { label: $t('page.login.register.roleAdmin'), value: 'admin' },
          { label: $t('page.login.register.roleUser'), value: 'user' }
        ]"
      />
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
