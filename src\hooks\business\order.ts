import { reactive, ref } from 'vue';
import { fetchOrderList } from '@/service/api';

/**
 * 订单管理hook
 */
export function useOrderManagement() {
  const loading = ref(false);
  const orderList = ref<Api.Order.Order[]>([]);
  
  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100]
  });

  // 搜索参数
  const searchParams = reactive<Api.Order.GetOrderListParams>({
    page: 1,
    page_size: 10,
    order_no: '',
    status: '',
    user_id: undefined,
    purchaser_id: undefined
  });

  /**
   * 获取订单列表
   */
  async function getOrderList() {
    try {
      loading.value = true;
      
      const params: Api.Order.GetOrderListParams = {
        page: pagination.page,
        page_size: pagination.pageSize,
        ...searchParams
      };

      // 过滤空值参数
      Object.keys(params).forEach(key => {
        const value = params[key as keyof Api.Order.GetOrderListParams];
        if (value === '' || value === undefined || value === null) {
          delete params[key as keyof Api.Order.GetOrderListParams];
        }
      });

      const { data, error } = await fetchOrderList(params);
      console.log('data',data)
      if (!error && data?.list) {
        orderList.value = data.list || [];
        pagination.total = data.total || 0;
        pagination.page = data.page || 1;
        pagination.pageSize = data.page_size || 10;
      } else {
        window.$message?.error('获取订单列表失败');
        orderList.value = [];
        pagination.total = 0;
      }
    } catch (err) {
      window.$message?.error('获取订单列表异常');
      orderList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 搜索订单
   */
  function searchOrders(params?: Partial<Api.Order.GetOrderListParams>) {
    Object.assign(searchParams, params);
    pagination.page = 1;
    getOrderList();
  }

  /**
   * 重置搜索
   */
  function resetSearch() {
    Object.keys(searchParams).forEach(key => {
      if (key === 'page' || key === 'page_size') {
        return;
      }
      (searchParams as any)[key] = key === 'page' ? 1 : key === 'page_size' ? 10 : '';
    });
    pagination.page = 1;
    getOrderList();
  }

  /**
   * 分页变化
   */
  function handlePageChange(page: number) {
    pagination.page = page;
    searchParams.page = page;
    getOrderList();
  }

  /**
   * 页面大小变化
   */
  function handlePageSizeChange(pageSize: number) {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    searchParams.page = 1;
    searchParams.page_size = pageSize;
    getOrderList();
  }

  /**
   * 根据状态筛选订单
   */
  function filterByStatus(status: string) {
    searchParams.status = status === 'all' ? '' : status;
    pagination.page = 1;
    getOrderList();
  }

  return {
    loading,
    orderList,
    pagination,
    searchParams,
    getOrderList,
    searchOrders,
    resetSearch,
    handlePageChange,
    handlePageSizeChange,
    filterByStatus
  };
}

/**
 * 订单状态映射
 */
export function useOrderStatus() {
  const statusMap: Record<string, { type: string; text: string; color: string }> = {
    pending: { type: 'warning', text: '待拍单', color: '#f0a020' },
    processing: { type: 'info', text: '拍单中', color: '#2080f0' },
    purchased: { type: 'primary', text: '已拍单', color: '#18a058' },
    shipped: { type: 'tertiary', text: '待发货', color: '#7c3aed' },
    completed: { type: 'success', text: '已完成', color: '#18a058' },
    cancelled: { type: 'error', text: '已取消', color: '#d03050' }
  };

  function getStatusInfo(status: string) {
    return statusMap[status] || { type: 'default', text: status, color: '#666' };
  }

  function getStatusOptions() {
    return Object.entries(statusMap).map(([value, config]) => ({
      label: config.text,
      value
    }));
  }

  return {
    statusMap,
    getStatusInfo,
    getStatusOptions
  };
}
