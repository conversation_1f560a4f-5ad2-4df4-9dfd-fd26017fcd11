import { computed } from 'vue';
import { usePermission } from '@/utils/permission';

/**
 * 权限管理组合式函数
 */
export function useAuth() {
  const {
    userMenus,
    userPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getUserMenuPermissions,
    getAccessibleRoutes
  } = usePermission();

  /**
   * 检查是否有指定权限
   */
  const checkPermission = (permission: string | string[], requireAll = false) => {
    if (Array.isArray(permission)) {
      return requireAll ? hasAllPermissions(permission) : hasAnyPermission(permission);
    }
    return hasPermission(permission);
  };

  /**
   * 权限相关的计算属性
   */
  const authState = computed(() => ({
    // 是否是管理员
    isAdmin: hasPermission('admin') || hasPermission('system:admin'),
    
    // 用户管理权限
    canViewUsers: hasPermission('system:user:list'),
    canAddUser: hasPermission('system:user:add'),
    canEditUser: hasPermission('system:user:edit'),
    canDeleteUser: hasPermission('system:user:delete'),
    
    // 菜单管理权限
    canViewMenus: hasPermission('system:menu:list'),
    canAddMenu: hasPermission('system:menu:add'),
    canEditMenu: hasPermission('system:menu:edit'),
    canDeleteMenu: hasPermission('system:menu:delete'),
    
    // 订单管理权限
    canViewOrders: hasPermission('order:list') || hasPermission('order:view'),
    canEditOrder: hasPermission('order:edit'),
    canDeleteOrder: hasPermission('order:delete'),
    canCreateOrder: hasPermission('order:create'),
    
    // 采购管理权限
    canViewPurchaseTasks: hasPermission('purchase:task:list'),
    canViewPurchaseRecords: hasPermission('purchase:record:list')
  }));

  /**
   * 获取用户可操作的按钮权限
   */
  const getButtonPermissions = (module: string) => {
    const permissions = userPermissions.value.filter(p => p.startsWith(`${module}:`));
    return {
      canView: permissions.some(p => p.includes(':list') || p.includes(':view')),
      canAdd: permissions.some(p => p.includes(':add') || p.includes(':create')),
      canEdit: permissions.some(p => p.includes(':edit') || p.includes(':update')),
      canDelete: permissions.some(p => p.includes(':delete')),
      canExport: permissions.some(p => p.includes(':export')),
      canImport: permissions.some(p => p.includes(':import'))
    };
  };

  /**
   * 过滤表格操作按钮
   */
  const filterTableActions = (actions: Array<{
    label: string;
    permission?: string | string[];
    action: () => void;
  }>) => {
    return actions.filter(action => {
      if (!action.permission) return true;
      return checkPermission(action.permission);
    });
  };

  /**
   * 检查路由权限
   */
  const canAccessRoute = (routePath: string) => {
    const accessibleRoutes = getAccessibleRoutes();
    
    function findRoute(routes: Api.Menu.Response[], path: string): boolean {
      return routes.some(route => {
        if (route.path === path) return true;
        if (route.children) {
          return findRoute(route.children, path);
        }
        return false;
      });
    }
    
    return findRoute(accessibleRoutes, routePath);
  };

  return {
    // 基础权限方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    checkPermission,
    
    // 权限状态
    authState,
    userMenus,
    userPermissions,
    
    // 工具方法
    getUserMenuPermissions,
    getAccessibleRoutes,
    getButtonPermissions,
    filterTableActions,
    canAccessRoute
  };
}
