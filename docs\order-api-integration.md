# 订单管理API对接文档

## 概述

本文档描述了订单管理系统的API接口对接，包括获取订单列表、订单详情、创建订单等功能。

## API接口

### 获取订单列表接口
- **路径**: `/api/orders`
- **方法**: GET
- **参数类型**: `Api.Order.GetOrderListParams`
- **返回类型**: `Api.Order.Response<Api.Order.GetOrderListResponse>`

### 获取订单详情接口
- **路径**: `/api/orders/{id}`
- **方法**: GET
- **返回类型**: `Api.Order.Response<Api.Order.Order>`

### 创建订单接口
- **路径**: `/api/orders`
- **方法**: POST
- **参数类型**: `Partial<Api.Order.Order>`
- **返回类型**: `Api.Order.Response<Api.Order.Order>`

### 更新订单接口
- **路径**: `/api/orders/{id}`
- **方法**: PUT
- **参数类型**: `Partial<Api.Order.Order>`
- **返回类型**: `Api.Order.Response<Api.Order.Order>`

### 删除订单接口
- **路径**: `/api/orders/{id}`
- **方法**: DELETE
- **返回类型**: `Api.Order.Response<null>`

## 类型定义

### 订单状态枚举
```typescript
enum OrderStatus {
  Pending = 'pending',      // 待处理
  Processing = 'processing', // 处理中
  Purchased = 'purchased',   // 已采购
  Completed = 'completed',   // 已完成
  Cancelled = 'cancelled'    // 已取消
}
```

### 订单信息
```typescript
interface Order {
  id?: number;
  order_no?: string;
  platform_order_no?: string;
  user_id?: number;
  user?: User;
  purchaser_id?: number;
  purchaser?: User;
  buyer_name?: string;
  receiver_name?: string;
  receiver_phone?: string;
  receiver_address?: string;
  receiver_city?: string;
  receiver_state?: string;
  postal_code?: string;
  product_id?: string;
  product_name?: string;
  product_image_url?: string;
  product_price?: number;
  product_quantity?: number;
  product_specs?: string;
  asin_sku?: string;
  order_amount?: number;
  order_message?: string;
  payment_time?: string;
  ship_deadline?: string;
  shop_account?: string;
  status?: OrderStatus;
  remark?: string;
  purchase_record?: PurchaseRecord;
  created_at?: string;
  updated_at?: string;
}
```

### 查询参数
```typescript
interface GetOrderListParams {
  page?: number;           // 页码
  page_size?: number;      // 每页数量
  order_no?: string;       // 订单号
  status?: string;         // 订单状态
  user_id?: number;        // 用户ID
  purchaser_id?: number;   // 采购员ID
}
```

### 响应数据
```typescript
interface GetOrderListResponse {
  list?: Order[];          // 订单列表
  page?: number;           // 当前页码
  page_size?: number;      // 每页数量
  total?: number;          // 总数量
}
```

## 基本使用

### 获取订单列表
```typescript
import { fetchOrderList } from '@/service/api/order';

// 获取所有订单
const { data, error } = await fetchOrderList();

// 带查询参数
const { data, error } = await fetchOrderList({
  page: 1,
  page_size: 10,
  status: 'pending'
});
```

### 获取订单详情
```typescript
import { fetchOrderDetail } from '@/service/api/order';

const { data, error } = await fetchOrderDetail(123);
```

### 创建订单
```typescript
import { createOrder } from '@/service/api/order';

const orderData = {
  order_no: 'ORDER123',
  product_name: 'Test Product',
  buyer_name: 'Test Buyer',
  order_amount: 100.50
};

const { data, error } = await createOrder(orderData);
```

## 在组件中使用

### 使用订单管理Hook
```typescript
import { useOrderManagement } from '@/hooks/business/order';

const {
  loading,
  orderList,
  pagination,
  getOrderList,
  searchOrders,
  resetSearch
} = useOrderManagement();

// 获取订单列表
await getOrderList();

// 搜索订单
searchOrders({ status: 'pending' });

// 重置搜索
resetSearch();
```

### 使用订单状态Hook
```typescript
import { useOrderStatus } from '@/hooks/business/order';

const { getStatusInfo, getStatusOptions } = useOrderStatus();

// 获取状态信息
const statusInfo = getStatusInfo('pending');
// { type: 'warning', text: '待处理', color: '#f0a020' }

// 获取状态选项
const options = getStatusOptions();
// [{ label: '待处理', value: 'pending' }, ...]
```

## 错误处理

所有API调用都返回统一的响应格式：
```typescript
interface Response<T> {
  code?: number;    // 响应状态码
  data?: T;         // 响应数据
  message?: string; // 响应消息
}
```

建议在调用API时进行错误处理：
```typescript
const { data, error } = await fetchOrderList();

if (!error && data?.data) {
  // 处理成功响应
  console.log('订单列表:', data.data.list);
} else {
  // 处理错误
  console.error('获取订单列表失败:', error || data?.message);
}
```

## 注意事项

1. 所有时间字段使用ISO 8601格式字符串
2. 金额字段使用数字类型，单位为元
3. 订单状态使用枚举值，确保类型安全
4. 分页参数从1开始计数
5. 查询参数中的空值会被自动过滤
