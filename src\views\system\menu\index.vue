<script lang="ts" setup>
import { ref, onMounted, h, reactive } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag, NSpace, NIcon, useMessage, NDataTable, NModal, NForm, NFormItem, NInput, NSelect, NSwitch, NInputNumber } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchMenuTree, updateMenu, createMenu, deleteMenu } from '@/service/api';

defineOptions({
  name: 'MenuManagement'
});

const message = useMessage();

// 菜单数据
const menuData = ref<Api.Menu.Response[]>([]);
const loading = ref(false);

// 弹窗状态
const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean();
const isEdit = ref(false);
const currentMenuId = ref<number | undefined>();

// 菜单表单数据
const menuFormData = reactive<Api.Menu.UpdateMenuRequest>({
  code: '',
  name: '',
  path: '',
  component: '',
  icon: '',
  permission: '',
  description: '',
  parent_id: 0,
  type: 1,
  status: 1,
  sort: 0,
  hidden: false,
  keep_alive: true,
  redirect: ''
});

// 菜单类型选项
const menuTypeOptions = [
  { label: '菜单', value: 1 },
  { label: '按钮', value: 2 }
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
];

// 菜单类型映射
const menuTypeMap = {
  1: { label: '菜单', type: 'info' as const },
  2: { label: '按钮', type: 'warning' as const }
};

// 状态映射
const statusMap = {
  1: { label: '启用', type: 'success' as const },
  0: { label: '禁用', type: 'error' as const }
};

// 表格列定义
const columns: DataTableColumns<Api.Menu.Response> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '菜单名称',
    key: 'name',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    tree: true
  },
  {
    title: '菜单编码',
    key: 'code',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '路径',
    key: 'path',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '图标',
    key: 'icon',
    width: 100,
    align: 'center',
    render(row) {
      return row.icon ? h('div', { class: 'flex justify-center' }, [
        h(NIcon, { size: 18 }, {
          default: () => h('i', { class: `icon-${row.icon}` })
        })
      ]) : '-';
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    align: 'center',
    render(row) {
      const typeInfo = menuTypeMap[row.type as keyof typeof menuTypeMap];
      return typeInfo ? h(NTag, { type: typeInfo.type }, { default: () => typeInfo.label }) : '-';
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      const statusInfo = statusMap[row.status as keyof typeof statusMap];
      return statusInfo ? h(NTag, { type: statusInfo.type }, { default: () => statusInfo.label }) : '-';
    }
  },
  {
    title: '排序',
    key: 'sort',
    width: 80,
    align: 'center'
  },
  {
    title: '层级',
    key: 'level',
    width: 80,
    align: 'center'
  },
  {
    title: '权限标识',
    key: 'permission',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render(row) {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleEdit(row)
          }, {
            default: () => '编辑'
          }),
          h(NButton, {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => handleDelete(row)
          }, {
            default: () => '删除'
          })
        ]
      });
    }
  }
];

// 获取菜单树数据
const getMenuTree = async () => {
  try {
    loading.value = true;
    const { data, error } = await fetchMenuTree();
    console.log('data', data);
    if (!error && data) {
      menuData.value = data;
    } else {
      message.error('获取菜单数据失败');
    }
  } catch (error) {
    console.error('获取菜单数据失败:', error);
    message.error('获取菜单数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const handleRefresh = () => {
  getMenuTree();
};

// 重置表单
const resetForm = () => {
  Object.assign(menuFormData, {
    code: '',
    name: '',
    path: '',
    component: '',
    icon: '',
    permission: '',
    description: '',
    parent_id: 0,
    type: 1,
    status: 1,
    sort: 0,
    hidden: false,
    keep_alive: true,
    redirect: ''
  });
};

// 新增菜单
const handleAdd = () => {
  isEdit.value = false;
  currentMenuId.value = undefined;
  resetForm();
  openModal();
};

// 编辑菜单
const handleEdit = (row: Api.Menu.Response) => {
  isEdit.value = true;
  currentMenuId.value = row.id;
  Object.assign(menuFormData, {
    code: row.code || '',
    name: row.name || '',
    path: row.path || '',
    component: row.component || '',
    icon: row.icon || '',
    permission: row.permission || '',
    description: row.description || '',
    parent_id: row.parent_id || 0,
    type: row.type || 1,
    status: row.status || 1,
    sort: row.sort || 0,
    hidden: row.hidden || false,
    keep_alive: row.keep_alive || true,
    redirect: row.redirect || ''
  });
  openModal();
};

// 提交菜单表单
const handleSubmitMenu = async () => {
  if (!menuFormData.name) {
    message.warning('请输入菜单名称');
    return;
  }
  if (!menuFormData.code) {
    message.warning('请输入菜单编码');
    return;
  }

  try {
    if (isEdit.value && currentMenuId.value) {
      // 编辑菜单
      const { error } = await updateMenu(currentMenuId.value, menuFormData);
      if (!error) {
        message.success('菜单更新成功');
        closeModal();
        getMenuTree();
      } else {
        message.error('菜单更新失败');
      }
    } else {
      // 新增菜单
      const { error } = await createMenu(menuFormData as Api.Menu.CreateMenuRequest);
      if (!error) {
        message.success('菜单创建成功');
        closeModal();
        getMenuTree();
      } else {
        message.error('菜单创建失败');
      }
    }
  } catch (error) {
    console.error('提交菜单表单失败:', error);
    message.error('操作失败');
  }
};

// 删除菜单
const handleDelete = async (row: Api.Menu.Response) => {
  if (!row.id) {
    message.warning('菜单ID不能为空');
    return;
  }

  window.$dialog?.warning({
    title: '确认删除',
    content: `确定要删除菜单"${row.name}"吗？`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const { error } = await deleteMenu(row.id!);
        if (!error) {
          message.success('菜单删除成功');
          getMenuTree();
        } else {
          message.error('菜单删除失败');
        }
      } catch (error) {
        console.error('删除菜单失败:', error);
        message.error('删除失败');
      }
    }
  });
};



// 组件挂载时获取数据
onMounted(() => {
  getMenuTree();
});
</script>

<template>
  <div class="h-full flex-col-stretch gap-16px overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-y-center justify-between">
      <div>
        <h2 class="text-18px font-bold">菜单管理</h2>
        <p class="text-14px text-gray-500 mt-4px">管理系统菜单和权限配置</p>
      </div>

      <NSpace>
        <NButton type="primary" @click="handleAdd">
          新增菜单
        </NButton>

        <NButton @click="handleRefresh" :loading="loading">
          刷新
        </NButton>
      </NSpace>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1-hidden">
      <NDataTable
        :columns="columns"
        :data="menuData"
        :loading="loading"
        :scroll-x="1400"
        :row-key="(row: Api.Menu.Response) => row.id || 0"
        :children-key="'children'"
        class="h-full"
        flex-height
        striped
        bordered
      />
    </div>

    <!-- 新增/编辑菜单弹出框 -->
    <NModal
      v-model:show="modalVisible"
      preset="card"
      :title="isEdit ? '编辑菜单' : '新增菜单'"
      class="w-800px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm
        :model="menuFormData"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <div class="grid grid-cols-2 gap-16px">
          <NFormItem label="菜单名称" required>
            <NInput v-model:value="menuFormData.name" placeholder="请输入菜单名称" clearable />
          </NFormItem>
          <NFormItem label="菜单编码" required>
            <NInput v-model:value="menuFormData.code" placeholder="请输入菜单编码" clearable />
          </NFormItem>
          <NFormItem label="菜单路径">
            <NInput v-model:value="menuFormData.path" placeholder="请输入菜单路径" clearable />
          </NFormItem>
          <NFormItem label="组件路径">
            <NInput v-model:value="menuFormData.component" placeholder="请输入组件路径" clearable />
          </NFormItem>
          <NFormItem label="菜单图标">
            <NInput v-model:value="menuFormData.icon" placeholder="请输入菜单图标" clearable />
          </NFormItem>
          <NFormItem label="权限标识">
            <NInput v-model:value="menuFormData.permission" placeholder="请输入权限标识" clearable />
          </NFormItem>
          <NFormItem label="父级菜单ID">
            <NInputNumber v-model:value="menuFormData.parent_id" placeholder="请输入父级菜单ID" class="w-full" />
          </NFormItem>
          <NFormItem label="菜单类型">
            <NSelect
              v-model:value="menuFormData.type"
              :options="menuTypeOptions"
              placeholder="请选择菜单类型"
            />
          </NFormItem>
          <NFormItem label="状态">
            <NSelect
              v-model:value="menuFormData.status"
              :options="statusOptions"
              placeholder="请选择状态"
            />
          </NFormItem>
          <NFormItem label="排序">
            <NInputNumber v-model:value="menuFormData.sort" placeholder="请输入排序值" class="w-full" />
          </NFormItem>
          <NFormItem label="是否隐藏">
            <NSwitch v-model:value="menuFormData.hidden" />
          </NFormItem>
          <NFormItem label="是否缓存">
            <NSwitch v-model:value="menuFormData.keep_alive" />
          </NFormItem>
        </div>
        <NFormItem label="重定向路径">
          <NInput v-model:value="menuFormData.redirect" placeholder="请输入重定向路径" clearable />
        </NFormItem>
        <NFormItem label="菜单描述">
          <NInput
            v-model:value="menuFormData.description"
            type="textarea"
            placeholder="请输入菜单描述"
            :rows="3"
            clearable
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmitMenu">
            {{ isEdit ? '更新' : '创建' }}
          </NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style lang="scss">

</style>
