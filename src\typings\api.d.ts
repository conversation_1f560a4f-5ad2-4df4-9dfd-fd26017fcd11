/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    /** 用户角色枚举 */
    enum UserRole {
      /** 普通用户 */
      AUser = 'a_user',
      /** 管理员 */
      Admin = 'admin',
      /** 采购员 */
      Purchaser = 'purchaser'
    }
    /** 登录请求参数 */
    interface LoginParams {
      phone: string;
      password: string;
    }

    /** 登录响应数据 */
    interface LoginToken {
      /** 访问token */
      token: string;
      /** 刷新token */
      refreshToken: string;
    }

    /** 用户信息 */
    interface UserInfo {
      /** 用户ID */
      id?: number;
      /** 用户名 */
      username?: string;
      /** 手机号 */
      phone?: string;
      /** 用户角色 */
      role?: UserRole;
      /** 用户状态 */
      status?: number;
      /** 创建时间 */
      created_at?: string;
      /** 更新时间 */
      updated_at?: string;
    }

    /** 刷新token请求参数 */
    interface RefreshTokenParams {
      refreshToken: string;
    }

    /** 注册请求参数 */
    interface RegisterParams {
      /** 手机号 */
      phone: string;
      /** 密码 */
      password: string;
      /** 用户名 */
      username: string;
      /** 用户角色 */
      role: string;
    }

    /** 注册响应数据 */
    interface RegisterResult {
      /** 注册是否成功 */
      success: boolean;
      /** 消息 */
      message?: string;
    }

    /** 发送短信验证码请求参数 */
    interface SendSmsParams {
      /** 手机号 */
      phone: string;
    }

    /** 发送短信验证码响应数据 */
    interface SendSmsResult {
      /** 发送是否成功 */
      success: boolean;
      /** 消息 */
      message?: string;
    }

    /** 短信验证码登录请求参数 */
    interface SmsLoginParams {
      /** 手机号 */
      phone: string;
      /** 验证码 */
      code: string;
      /** 用户名 */
      username: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace Menu
   *
   * backend api module: "menu"
   */
  namespace Menu {

    /** 菜单树节点 */
    interface Response {
      children?: Api.Menu.Response[];
      code?: string;
      component?: string;
      created_at?: string;
      description?: string;
      hidden?: boolean;
      icon?: string;
      id?: number;
      keep_alive?: boolean;
      level?: number;
      name?: string;
      parent_id?: number;
      path?: string;
      permission?: string;
      redirect?: string;
      sort?: number;
      status?: number;
      type?: number;
      updated_at?: string;
      [property: string]: any;
    }

    /** 更新菜单请求参数 */
    interface UpdateMenuRequest {
      code?: string;
      component?: string;
      description?: string;
      hidden?: boolean;
      icon?: string;
      keep_alive?: boolean;
      name?: string;
      parent_id?: number;
      path?: string;
      permission?: string;
      redirect?: string;
      sort?: number;
      status?: number;
      type?: number;
      [property: string]: any;
    }

    /** 创建菜单请求参数 */
    interface CreateMenuRequest {
      code: string;
      component?: string;
      description?: string;
      hidden?: boolean;
      icon?: string;
      keep_alive?: boolean;
      name: string;
      parent_id?: number;
      path?: string;
      permission?: string;
      redirect?: string;
      sort?: number;
      status?: number;
      type: number;
    }

    /** 用户菜单权限响应 */
    interface UserMenuResponse {
      code: number;
      message: string;
      data: Response[];
    }
  }

  /**
   * namespace Order
   *
   * backend api module: "order"
   */
  namespace Order {
    /** 获取订单详情请求参数 */
    interface GetOrderDetailParams {
      /** 订单ID */
      id: number;
    }

    /** 订单状态枚举 */
    enum OrderStatus {
      /** 待拍单 */
      Pending = 'pending',
      /** 拍单中 */
      Processing = 'processing',
      /** 已拍单 */
      Purchased = 'purchased',
      /** 待发货 */
      Shipped = 'shipped',
      /** 已完成 */
      Completed = 'completed',
      /** 已取消 */
      Cancelled = 'cancelled'
    }

    /** 用户角色枚举 */
    enum UserRole {
      /** 普通用户 */
      AUser = 'a_user',
      /** 管理员 */
      Admin = 'admin',
      /** 采购员 */
      Purchaser = 'purchaser'
    }

    /** 文件上传信息 */
    interface FileUpload {
      id?: number;
      file_name?: string;
      file_path?: string;
      file_size?: number;
      file_type?: string;
      record_id?: number;
      created_at?: string;
      updated_at?: string;
    }

    /** 用户信息 */
    interface User {
      id?: number;
      phone: string;
      role: UserRole;
      status?: number;
      username: string;
      created_at?: string;
      updated_at?: string;
    }

    /** 采购记录 */
    interface PurchaseRecord {
      id?: number;
      order_id?: number;
      b_order_no?: string;
      purchase_amount?: number;
      purchaser_id?: number;
      purchaser?: User;
      logistics_no?: string;
      status?: OrderStatus;
      remark?: string;
      files?: FileUpload[];
      created_at?: string;
      updated_at?: string;
    }

    /** 订单信息 */
    interface Order {
      id?: number;
      order_no?: string;
      platform_order_no?: string;
      user_id?: number;
      user?: User;
      purchaser_id?: number;
      purchaser?: User;
      buyer_name?: string;
      receiver_name?: string;
      receiver_phone?: string;
      receiver_address?: string;
      receiver_city?: string;
      receiver_state?: string;
      postal_code?: string;
      product_id?: string;
      product_name?: string;
      product_image_url?: string;
      product_price?: number;
      product_quantity?: number;
      product_specs?: string;
      asin_sku?: string;
      order_amount?: number;
      order_message?: string;
      payment_time?: string;
      ship_deadline?: string;
      shop_account?: string;
      status?: OrderStatus;
      remark?: string;
      purchase_record?: PurchaseRecord;
      created_at?: string;
      updated_at?: string;
    }

    /** 获取订单列表请求参数 */
    interface GetOrderListParams {
      /** 页码 */
      page?: number;
      /** 每页数量 */
      page_size?: number;
      /** 订单号 */
      order_no?: string;
      /** 订单状态 */
      status?: string;
      /** 用户ID */
      user_id?: number;
      /** 采购员ID */
      purchaser_id?: number;
    }

    /** 获取订单列表响应数据 */
    interface GetOrderListResponse {
      list?: Order[];
      page?: number;
      page_size?: number;
      total?: number;
    }

    /** 通用响应结构 */
    interface Response<T = any> {
      /** 响应状态码 */
      code?: number;
      /** 响应数据 */
      data?: T;
      /** 响应消息 */
      message?: string;
    }
    interface ExcelParseResponse {
      failed_list?: ExcelParseItem[];
      success_list?: ExcelParseItem[];
    }
    /** Excel解析的订单数据 */
    interface ExcelParseItem {
      /** P列：ASIN/SKU规格 */
      asin?: string;
      /** I列：买家姓名 */
      buyer_name?: string;
      /** R列：订单金额 */
      order_amount?: number;
      /** C列：订单付款时间 */
      payment_time?: string;
      /** A列：经营平台订单号 */
      platform_order_no?: string;
      /** N列：邮政编码 */
      postal_code?: string;
      /** F列：商品ID */
      product_id?: string;
      /** Q列：商品图片链接 */
      product_image_url?: string;
      /** G列：商品名称 */
      product_name?: string;
      /** H列：店铺商品售价 */
      product_price?: number;
      /** E列：商品规格 */
      product_spec?: string;
      /** T列：代拍商品链接 */
      purchase_url?: string;
      /** S列：商品数量 */
      quantity?: number;
      /** K列：收货人地址 */
      receiver_address?: string;
      /** L列：收货人城市 */
      receiver_city?: string;
      /** J列：收货人姓名 */
      receiver_name?: string;
      /** O列：收货人电话 */
      receiver_phone?: string;
      /** M列：收货人州/省 */
      receiver_state?: string;
      /** U列：备注 */
      remark?: string;
      /** D列：订单截止发货时间 */
      ship_deadline?: string;
      /** B列：店铺账号 */
      shop_account?: string;
      /** 失败原因（仅在failed_list中存在） */
      reason?: string;
    }

    /** 导入失败的订单信息 */
    interface FailedOrderInfo {
      /** 平台订单号 */
      platform_order_no?: string;
      /** 失败原因 */
      reason?: string;
    }

    /** 导入结果 */
    interface ImportResult {
      /** 成功数量 */
      success_count?: number;
      /** 失败数量 */
      failed_count?: number;
      /** 总数量 */
      total_count?: number;
      /** 成功的订单 */
      success_orders?: Order[];
      /** 失败的订单信息 */
      failed_orders?: FailedOrderInfo[];
    }

    /** 上传物流单请求参数 */
    interface UploadLogisticsRequest {
      /** 物流单图片路径数组 */
      image_paths: string[];
      /** 物流单号 */
      logistics_no: string;
    }

    /** 上传拍单信息请求参数 */
    interface UploadPurchaseInfoRequest {
      /** 拍单截图路径数组 */
      image_paths: string[];
      /** 拍单金额 */
      purchase_amount: number;
    }
  }

  /**
   * namespace User
   *
   * backend api module: "user"
   */
  namespace User {
    /** 用户角色枚举 */
    enum UserRole {
      /** 普通用户 */
      AUser = 'a_user',
      /** 管理员 */
      Admin = 'admin',
      /** 采购员 */
      Purchaser = 'purchaser'
    }

    /** 用户状态枚举 */
    enum UserStatus {
      /** 启用 */
      Active = 1,
      /** 禁用 */
      Inactive = 0
    }

    /** 用户信息 */
    interface User {
      id?: number;
      username: string;
      phone: string;
      password?: string;
      role: UserRole;
      status: UserStatus;
      created_at?: string;
      updated_at?: string;
    }

    /** 获取用户列表请求参数 */
    interface GetUserListParams {
      /** 页码 */
      page?: number;
      /** 每页数量 */
      page_size?: number;
      /** 用户名 */
      username?: string;
      /** 手机号 */
      phone?: string;
      /** 角色 */
      role?: UserRole;
      /** 状态 */
      status?: UserStatus;
    }

    /** 获取用户列表响应数据 */
    interface GetUserListResponse {
      list?: User[];
      page?: number;
      page_size?: number;
      total?: number;
    }

    /** 创建用户请求参数 */
    interface CreateUserRequest {
      username: string;
      phone: string;
      password: string;
      role: UserRole;
      status: UserStatus;
    }

    /** 更新用户请求参数 */
    interface UpdateUserRequest {
      username?: string;
      phone?: string;
      password?: string;
      role?: UserRole;
      status?: UserStatus;
    }

    /** 通用响应结构 */
    interface Response<T = any> {
      /** 响应状态码 */
      code?: number;
      /** 响应数据 */
      data?: T;
      /** 响应消息 */
      message?: string;
    }
  }
}
