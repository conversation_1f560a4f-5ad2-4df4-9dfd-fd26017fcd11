# CDN上传功能使用示例

## 概述

本文档提供了在订单管理系统中使用CDN上传功能的具体示例，展示如何将现有的文件上传功能升级为CDN上传。

## 在订单管理中的应用

### 1. 物流单图片上传

可以将现有的物流单图片上传功能升级为CDN上传：

```typescript
// 在 src/views/workspace/order/index.vue 中
import { uploadCDNImg, UploadWay } from '@/utils/common';

// 处理物流单图片上传 - CDN版本
async function handleLogisticsImageChangeCDN(fileList: any[]) {
  if (fileList.length > 0) {
    const file = fileList[0].file;
    if (file) {
      try {
        // 上传到CDN，使用logistics场景
        const cdnUrl = await uploadCDNImg(
          file,
          'logistics',           // 场景值：物流单
          `${Date.now()}.jpg`,   // 文件名：时间戳
          UploadWay.PROD,        // 正式环境
          (progress) => {        // 进度回调
            console.log(`物流单上传进度: ${progress}%`);
          }
        );
        
        // 保存CDN URL
        formData.value.logisticsImage = cdnUrl;
        window.$message?.success('物流单图片上传成功');
      } catch (error) {
        console.error('上传物流单图片失败:', error);
        formData.value.logisticsImage = '';
      }
    }
  } else {
    formData.value.logisticsImage = '';
  }
}
```

### 2. 拍单截图上传

拍单截图也可以使用CDN上传：

```typescript
// 处理拍单截图上传 - CDN版本
async function handlePurchaseImageChangeCDN(fileList: any[]) {
  if (fileList.length > 0) {
    const file = fileList[0].file;
    if (file) {
      try {
        // 上传到CDN，使用purchase场景
        const cdnUrl = await uploadCDNImg(
          file,
          'purchase',            // 场景值：拍单
          `${Date.now()}.jpg`,   // 文件名：时间戳
          UploadWay.PROD,        // 正式环境
          (progress) => {        // 进度回调
            console.log(`拍单截图上传进度: ${progress}%`);
          }
        );
        
        // 保存CDN URL
        purchaseFormData.value.productScreenshot = cdnUrl;
        window.$message?.success('拍单截图上传成功');
      } catch (error) {
        console.error('上传拍单截图失败:', error);
        purchaseFormData.value.productScreenshot = '';
      }
    }
  } else {
    purchaseFormData.value.productScreenshot = '';
  }
}
```

### 3. 带进度显示的上传

可以添加进度条显示上传进度：

```typescript
// 带进度显示的上传函数
async function uploadWithProgress(file: File, scene: string) {
  const uploadProgress = ref(0);
  const uploading = ref(false);
  
  try {
    uploading.value = true;
    
    const cdnUrl = await uploadCDNImg(
      file,
      scene,
      `${Date.now()}_${file.name}`,
      UploadWay.PROD,
      (progress) => {
        uploadProgress.value = progress;
        // 可以在这里更新UI进度条
      }
    );
    
    uploading.value = false;
    uploadProgress.value = 100;
    
    return cdnUrl;
  } catch (error) {
    uploading.value = false;
    uploadProgress.value = 0;
    throw error;
  }
}
```

### 4. 批量上传

支持批量上传多个文件：

```typescript
// 批量上传文件
async function uploadMultipleFiles(files: File[], scene: string) {
  const results = [];
  const total = files.length;
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    try {
      window.$message?.info(`正在上传第 ${i + 1}/${total} 个文件...`);
      
      const cdnUrl = await uploadCDNImg(
        file,
        scene,
        `${Date.now()}_${i}_${file.name}`,
        UploadWay.PROD
      );
      
      results.push({
        file: file.name,
        url: cdnUrl,
        success: true
      });
    } catch (error) {
      results.push({
        file: file.name,
        error: error.message,
        success: false
      });
    }
  }
  
  const successCount = results.filter(r => r.success).length;
  window.$message?.success(`批量上传完成：成功 ${successCount}/${total} 个文件`);
  
  return results;
}
```

## 在Vue组件中的完整示例

```vue
<template>
  <div>
    <!-- 物流单上传 -->
    <NFormItem label="物流单图片" required>
      <NUpload
        v-model:file-list="logisticsFileList"
        :max="1"
        list-type="image-card"
        accept="image/*"
        :custom-request="handleLogisticsUpload"
      >
        <NButton>上传物流单图片</NButton>
      </NUpload>
      <NProgress 
        v-if="logisticsUploading" 
        :percentage="logisticsProgress" 
        :show-indicator="true"
      />
    </NFormItem>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { uploadCDNImg, UploadWay } from '@/utils/common';

// 上传状态
const logisticsUploading = ref(false);
const logisticsProgress = ref(0);
const logisticsFileList = ref([]);

// 自定义上传处理
const handleLogisticsUpload = async ({ file, onFinish, onError }) => {
  try {
    logisticsUploading.value = true;
    logisticsProgress.value = 0;
    
    const cdnUrl = await uploadCDNImg(
      file.file,
      'logistics',
      `${Date.now()}_${file.file.name}`,
      UploadWay.PROD,
      (progress) => {
        logisticsProgress.value = progress;
      }
    );
    
    // 保存CDN URL
    formData.value.logisticsImage = cdnUrl;
    
    onFinish();
    logisticsUploading.value = false;
  } catch (error) {
    onError();
    logisticsUploading.value = false;
    logisticsProgress.value = 0;
  }
};
</script>
```

## 环境配置

### 开发环境
```typescript
// 开发环境使用测试CDN
const cdnUrl = await uploadCDNImg(file, 'test', '', UploadWay.TEST);
```

### 生产环境
```typescript
// 生产环境使用正式CDN
const cdnUrl = await uploadCDNImg(file, 'product', '', UploadWay.PROD);
```

## 错误处理最佳实践

```typescript
async function safeUploadToCDN(file: File, scene: string) {
  try {
    // 文件大小检查
    if (file.size > 10 * 1024 * 1024) { // 10MB
      throw new Error('文件大小不能超过10MB');
    }
    
    // 文件类型检查
    if (!file.type.startsWith('image/')) {
      throw new Error('只支持图片文件');
    }
    
    // 上传到CDN
    const cdnUrl = await uploadCDNImg(file, scene, '', UploadWay.PROD);
    
    return {
      success: true,
      url: cdnUrl
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}
```

## 性能优化建议

1. **图片压缩**：上传前压缩图片
2. **并发控制**：限制同时上传的文件数量
3. **缓存机制**：避免重复上传相同文件
4. **断点续传**：大文件支持断点续传

## 总结

通过使用新的CDN上传功能，可以：
- ✅ 提升文件上传性能
- ✅ 减少服务器存储压力
- ✅ 提供更好的用户体验
- ✅ 支持全球CDN加速
- ✅ 统一的错误处理和进度显示

所有现有的文件上传功能都可以无缝升级为CDN上传，只需要替换上传函数即可。
