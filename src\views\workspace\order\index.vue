<script lang="ts" setup>
import { h, reactive, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useBoolean } from '@sa/hooks';
import { useOrderManagement, useOrderStatus } from '@/hooks/business/order';
import { downloadTemplate, deleteOrder, cancelOrder, updateOrderStatus, batchUpdateOrderStatus, createOrder, uploadOrderExcel, importOrders, uploadLogistics, uploadFile, uploadPurchaseInfo } from '@/service/api/order';
import { NButton, NTag } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'OrderManagement'
});

const router = useRouter();

// 使用订单管理hook
const {
  loading,
  orderList,
  pagination,
  searchParams,
  getOrderList,
  searchOrders,
  resetSearch,
  handlePageChange,
  handlePageSizeChange,
  filterByStatus
} = useOrderManagement();

// 使用订单状态hook
const { getStatusInfo } = useOrderStatus();

// 标签页状态
const activeTab = ref('all');

// 搜索表单数据
interface SearchForm {
  orderSubmitTimeRange: [number, number] | null;
  customerPaymentTimeRange: [number, number] | null;
  deliveryDeadlineRange: [number, number] | null;
  expectedArrivalChinaRange: [number, number] | null;
  expectedArrivalUSRange: [number, number] | null;
  productName: string;
  proxyProductASIM: string;
  businessPlatformOrderNumber: string;
  trackingNumber: string;
  sufengOrderNumber: string;
  orderMessage: string;
  orderTags: string[];
  bOrderNumber: string;
  hasBOrderNumber: string;
  storeName: string;
}

const searchForm = reactive<SearchForm>({
  orderSubmitTimeRange: null,
  customerPaymentTimeRange: null,
  deliveryDeadlineRange: null,
  expectedArrivalChinaRange: null,
  expectedArrivalUSRange: null,
  productName: '',
  proxyProductASIM: '',
  businessPlatformOrderNumber: '',
  trackingNumber: '',
  sufengOrderNumber: '',
  orderMessage: '',
  orderTags: [],
  bOrderNumber: '',
  hasBOrderNumber: '',
  storeName: ''
});

// 筛选条件展开/收起状态
const { bool: searchExpanded, toggle: toggleSearchExpanded } = useBoolean(true);

// 弹出框控制
// const { bool: modalVisible, setFalse: closeModal } = useBoolean();
const { bool: importModalVisible, setTrue: openImportModal, setFalse: closeImportModal } = useBoolean();

// 订单数据类型（使用API类型）
type OrderData = Api.Order.Order;

// 标签页选项
const tabOptions = [
  { key: 'all', label: '全部' },
  { key: 'pending', label: '待拍单' },
  { key: 'processing', label: '拍单中' },
  { key: 'purchased', label: '已拍单' },
  { key: 'shipped', label: '待发货' },
  { key: 'completed', label: '已完成' },
  { key: 'cancelled', label: '已取消' }
];

// 订单标签配置（10种颜色）
const orderTagOptions = [
  { label: '紧急', value: 'urgent', color: 'error' },
  { label: 'VIP', value: 'vip', color: 'warning' },
  { label: '普通', value: 'normal', color: 'info' },
  { label: '折扣', value: 'discount', color: 'success' },
  { label: '预售', value: 'presale', color: 'primary' },
  { label: '补发', value: 'reissue', color: 'tertiary' },
  { label: '退换', value: 'return', color: 'error' },
  { label: '特殊', value: 'special', color: 'warning' },
  { label: '海外', value: 'overseas', color: 'info' },
  { label: '定制', value: 'custom', color: 'success' }
];

// 是否有B单号选项
const hasBOrderNumberOptions = [
  { label: '全部', value: '' },
  { label: '是', value: 'true' },
  { label: '否', value: 'false' }
];

// 批量设置标签状态
const { bool: batchTagModalVisible, setTrue: openBatchTagModal, setFalse: closeBatchTagModal } = useBoolean();
const selectedOrderIds = ref<string[]>([]);
const batchSelectedTags = ref<string[]>([]);

// 批量上传订单抽屉状态
const { bool: batchUploadDrawerVisible, setTrue: openBatchUploadDrawer, setFalse: closeBatchUploadDrawer } = useBoolean();

// 批量上传标签页状态
const batchUploadActiveTab = ref('success');

// 批量上传订单数据类型
interface BatchUploadOrderData {
  id: string;
  platform_order_no?: string;
  shop_account?: string;
  buyer_name?: string;
  product_name?: string;
  product_spec?: string;
  order_amount?: number;
  remark?: string;
  failureReason?: string;
}

// 识别成功的订单数据
const successOrderData = ref<BatchUploadOrderData[]>([]);

// 识别失败的订单数据
const failureOrderData = ref<BatchUploadOrderData[]>([]);

// 保存完整的解析数据用于提交
const fullOrderData = ref<Api.Order.ExcelParseItem[]>([]);

// 识别成功表格列配置
const successColumns = [
  {
    title: '平台订单号',
    key: 'platform_order_no',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '店铺账号',
    key: 'shop_account',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '买家姓名',
    key: 'buyer_name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '商品名称',
    key: 'product_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '商品规格',
    key: 'product_spec',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '订单金额',
    key: 'order_amount',
    width: 120,
    render: (row: BatchUploadOrderData) => {
      return row.order_amount ? `¥${row.order_amount.toFixed(2)}` : '-';
    }
  },
  {
    title: '备注',
    key: 'remark',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  }
];

// 识别失败表格列配置
const failureColumns = [
  {
    title: '失败原因',
    key: 'failureReason',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '平台订单号',
    key: 'platform_order_no',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '店铺账号',
    key: 'shop_account',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '买家姓名',
    key: 'buyer_name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '商品名称',
    key: 'product_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '备注',
    key: 'remark',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  }
];

// 分页配置已在useOrderManagement中定义

// 表格列定义
const columns = [
  {
    type: 'selection' as const,
    width: 50
  },
  // {
  //   title: '订单号',
  //   key: 'order_no',
  //   width: 140,
  //   ellipsis: {
  //     tooltip: true
  //   },
  //   render: (row: OrderData) => {
  //     return h(
  //       'span',
  //       {
  //         class: 'cursor-pointer text-primary hover:text-primary-hover',
  //         onClick: () => handleView(row.id)
  //       },
  //       row.order_no || '-'
  //     );
  //   }
  // },
  {
    title: '商品名称',
    key: 'product_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  // {
  //   title: 'ASIN/SKU',
  //   key: 'asin_sku',
  //   width: 140,
  //   ellipsis: {
  //     tooltip: true
  //   }
  // },
  {
    title: '平台订单号',
    key: 'platform_order_no',
    width: 160,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '买家姓名',
    key: 'buyer_name',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '订单状态',
    key: 'status',
    width: 100,
    render: (row: OrderData) => {
      const statusInfo = getStatusInfo(row.status || '');
      return h(NTag, { type: statusInfo.type as any, size: 'small' }, {default: () => statusInfo.text});
    }
  },
  {
    title: '汇率',
    key: 'exchange_rate',
    width: 100,
  },
  {
    title: '订单金额',
    key: 'order_amount',
    width: 120,
    render: (row: OrderData) => {
      return row.order_amount ? `¥${row.order_amount.toFixed(2)}` : '-';
    }
  },
  // {
  //   title: 'B单号',
  //   key: 'purchase_record',
  //   width: 140,
  //   ellipsis: {
  //     tooltip: true
  //   },
  //   render: (row: OrderData) => {
  //     return row.purchase_record?.b_order_no || '-';
  //   }
  // },
  // {
  //   title: '采购员',
  //   key: 'purchaser',
  //   width: 100,
  //   render: (row: OrderData) => {
  //     return row.purchaser?.username || '-';
  //   }
  // },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160,
    render: (row: OrderData) => {
      return row.created_at ? new Date(row.created_at).toLocaleString() : '-';
    }
  },
  {
    title: '操作',
    key: 'operate',
    width: 160,
    fixed: 'right' as const,
    render: (row: OrderData) => {
      const buttons = [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            title: '查看',
            onClick: () => handleView(row.id)
          },
          { default: () => '查看' }
        )
      ];

      // 只有待拍单状态才显示拍单按钮
      if (row.status === 'pending') {
        buttons.push(
          h(
            NButton,
            {
              size: 'small',
              type: 'success',
              title: '拍单',
              onClick: () => handleStartOrder(row.id)
            },
            { default: () => '拍单' }
          )
        );
      }
      // 上传物流单按钮 - 只有已拍单状态才显示
      if (row.status === 'purchased') {
        buttons.push(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              title: '上传物流单',
              onClick: () => handleUploadLogistics(row.id)
            },
            { default: () => '物流' }
          )
        );
      }
      // 只有待拍单状态才显示取消按钮
      if (row.status === 'pending') {
        buttons.push(
          h(
            NButton,
            {
              size: 'small',
              type: 'error',
              ghost: true,
              title: '取消订单',
              onClick: () => handleCancel(row.id)
            },
            { default: () => '取消订单' }
          )
        );
      }

      return h('div', { class: 'flex gap-8px flex-wrap' }, buttons);
    }
  }
];

// 标签页切换
function handleTabChange(value: string) {
  activeTab.value = value;
  filterByStatus(value);
}

// 搜索
function handleSearch() {
  const params: Partial<Api.Order.GetOrderListParams> = {
    order_no: searchForm.sufengOrderNumber || undefined,
    status: activeTab.value === 'all' ? undefined : activeTab.value
  };
  searchOrders(params);
}

// 重置搜索
function handleReset() {
  Object.keys(searchForm).forEach(key => {
    (searchForm as any)[key] = '';
  });
  resetSearch();
}

// 新增订单
function handleAddOrder() {
  openBatchUploadDrawer();
}

// 手动新建订单
function handleManualCreateOrder() {
  openCreateOrderModal();
}

// 批量导入
function handleBatchImport() {
  openImportModal();
}
async function handleBatchCancel() {
  if (selectedOrderIds.value.length === 0) {
    window.$message?.warning('请先选择要取消的订单');
    return;
  }

  window.$dialog?.warning({
    title: '确认批量取消',
    content: `确定要取消选中的 ${selectedOrderIds.value.length} 个订单吗？取消后订单状态将变为已取消。`,
    positiveText: '确认取消',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 将选中的订单ID转换为数字数组
        const orderIds = selectedOrderIds.value.map(id => parseInt(id, 10));
        
        // 调用批量更新状态API，将状态设置为'cancelled'
        const { error } = await batchUpdateOrderStatus(orderIds, Api.Order.OrderStatus.Cancelled);
        
        if (!error) {
          window.$message?.success(`成功取消 ${selectedOrderIds.value.length} 个订单`);
          // 清空选中项
          selectedOrderIds.value = [];
          // 刷新订单列表
          searchOrders();
        } else {
          window.$message?.error('批量取消订单失败');
        }
      } catch (error) {
        console.error('批量取消订单失败:', error);
        window.$message?.error('批量取消订单失败');
      }
    }
  });
}
// 查看订单详情
function handleView(id?: number) {
  if (!id) return;
  router.push({
    path: '/workspace/order-detail',
    query: { id: id.toString() }
  });
}

// 开始拍单
function handleStartOrder(id?: number) {
  if (!id) return;

  // 保存当前操作的订单ID
  currentOrderId.value = id;
  openPurchaseModal();
}

// 提交拍单
async function handleSubmitPurchase() {
  if (!purchaseFormData.value.productScreenshot) {
    window.$message?.warning('请上传商品页面截图');
    return;
  }
  if (!purchaseFormData.value.productPrice) {
    window.$message?.warning('请填写商品价格');
    return;
  }
  if (!currentOrderId.value) {
    window.$message?.warning('请选择要拍单的订单');
    return;
  }

  try {
    // 调用上传拍单信息API
    const { error: uploadError } = await uploadPurchaseInfo(currentOrderId.value, {
      image_paths: [purchaseFormData.value.productScreenshot],
      purchase_amount: purchaseFormData.value.productPrice
    });

    if (!uploadError) {
      // 更新订单状态为拍单中
      const { error: statusError } = await updateOrderStatus(currentOrderId.value, 'processing');
      if (!statusError) {
        window.$message?.success('拍单提交成功');

        // 重置表单和订单ID
        purchaseFormData.value = {
          productScreenshot: '',
          productPrice: null
        };
        currentOrderId.value = undefined;

        closePurchaseModal();
        // 刷新订单列表
        searchOrders();
      } else {
        window.$message?.error('更新订单状态失败');
      }
    } else {
      window.$message?.error('拍单信息上传失败');
    }
  } catch (error) {
    console.error('拍单提交失败:', error);
    window.$message?.error('拍单提交失败');
  }
}


// 取消订单
async function handleCancel(id?: number) {
  if (!id) return;

  window.$dialog?.warning({
    title: '确认取消',
    content: `确定要取消订单 ${id} 吗？取消后订单状态将变为已取消。`,
    positiveText: '确认取消',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const { error } = await cancelOrder(id);
        if (!error) {
          window.$message?.success(`订单 ${id} 已取消`);
          // 刷新订单列表
          searchOrders();
        } else {
          window.$message?.error('取消订单失败');
        }
      } catch (error) {
        console.error('取消订单失败:', error);
        window.$message?.error('取消订单失败');
      }
    }
  });
}


// 导出订单
function handleExport() {
  window.$message?.info('导出功能开发中...');
}

// 下载模板
function handleDownloadTemplate() {
  downloadTemplate().then(response => {
    if (response.error) {
      window.$message?.error('下载模板失败');
      return;
    }
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response.data);
    const link = document.createElement('a');
    link.href = url;
    link.download = '订单导入模板.xlsx'; // 设置下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }).catch(error => {
    console.error('下载模板失败:', error);
    window.$message?.error('下载模板失败');
  });
}

// 批量设置标签
function handleBatchSetTags() {
  if (selectedOrderIds.value.length === 0) {
    window.$message?.warning('请先选择要设置标签的订单');
    return;
  }
  openBatchTagModal();
}

// 确认批量设置标签
function handleConfirmBatchTags() {
  if (batchSelectedTags.value.length === 0) {
    window.$message?.warning('请选择要设置的标签');
    return;
  }

  window.$message?.success(`已为 ${selectedOrderIds.value.length} 个订单设置标签`);

  // 重置状态
  selectedOrderIds.value = [];
  batchSelectedTags.value = [];
  closeBatchTagModal();
}

// 表格行选择
function handleRowSelection(rowKeys: Array<string | number>) {
  selectedOrderIds.value = rowKeys.map(key => String(key));
}

const orderManagement = ref<HTMLElement>();

const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean();
// 手动新建订单弹窗状态
const { bool: createOrderModalVisible, setTrue: openCreateOrderModal, setFalse: closeCreateOrderModal } = useBoolean();
// 拍单弹窗状态
const { bool: purchaseModalVisible, setTrue: openPurchaseModal, setFalse: closePurchaseModal } = useBoolean();
// 当前操作的订单ID
const currentOrderId = ref<number | undefined>();

// 上传物流单
function handleUploadLogistics(orderId?: number) {
  // 保存当前操作的订单ID
  currentOrderId.value = orderId;
  openModal();
}
// 表单数据
interface LogisticsForm {
  logisticsNumber: string;
  logisticsImage: string;
}

const formData = ref<LogisticsForm>({
  logisticsNumber: '',
  logisticsImage: ''
});

// 拍单表单数据
interface PurchaseForm {
  productScreenshot: string;
  productPrice: number | null;
}

const purchaseFormData = ref<PurchaseForm>({
  productScreenshot: '',
  productPrice: null
});

// 文件上传相关
const logisticsFileList = ref([]);
const purchaseFileList = ref([]);
const orderFileList = ref([]);
const uploadLoading = ref(false);

// 处理物流单图片上传
async function handleLogisticsImageChange(fileList: any[]) {
  if (fileList.length > 0) {
    const file = fileList[0].file;
    if (file) {
      try {
        const { data, error } = await uploadFile(file);
        if (!error && data) {
          formData.value.logisticsImage = data.file_path;
        } else {
          window.$message?.error('图片上传失败');
          formData.value.logisticsImage = '';
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        window.$message?.error('图片上传失败');
        formData.value.logisticsImage = '';
      }
    }
  } else {
    formData.value.logisticsImage = '';
  }
}

// 处理拍单截图上传
async function handlePurchaseImageChange(fileList: any[]) {
  if (fileList.length > 0) {
    const file = fileList[0].file;
    if (file) {
      try {
        const { data, error } = await uploadFile(file);
        if (!error && data) {
          purchaseFormData.value.productScreenshot = data.file_path;
        } else {
          window.$message?.error('截图上传失败');
          purchaseFormData.value.productScreenshot = '';
        }
      } catch (error) {
        console.error('上传截图失败:', error);
        window.$message?.error('截图上传失败');
        purchaseFormData.value.productScreenshot = '';
      }
    }
  } else {
    purchaseFormData.value.productScreenshot = '';
  }
}

// 处理订单文件上传
async function handleOrderFileUpload(fileList: any[]) {
  if (fileList.length === 0) {
    successOrderData.value = [];
    failureOrderData.value = [];
    return;
  }

  const file = fileList[0].file;
  if (!file) return;

  uploadLoading.value = true;

  try {
    const { data, error } = await uploadOrderExcel(file);

    if (!error && data) {
      // 保存完整的解析数据用于提交
      fullOrderData.value = data.success_list || [];

      // 将API返回的数据转换为表格显示格式
      const processedData = data.success_list?.map((item: Api.Order.ExcelParseItem, index: number) => ({
        id: (index + 1).toString(),
        platform_order_no: item.platform_order_no,
        shop_account: item.shop_account,
        buyer_name: item.buyer_name,
        product_name: item.product_name,
        product_spec: item.product_spec,
        order_amount: item.order_amount,
        remark: item.remark
      })) || [];

      // 处理失败数据
      const failedData = data.failed_list?.map((item: Api.Order.ExcelParseItem, index: number) => ({
        id: (index + 1).toString(),
        platform_order_no: item.platform_order_no,
        shop_account: item.shop_account,
        buyer_name: item.buyer_name,
        product_name: item.product_name,
        product_spec: item.product_spec,
        order_amount: item.order_amount,
        remark: item.remark,
        failureReason: item.reason || '解析失败'
      })) || [];

      successOrderData.value = processedData;
      failureOrderData.value = failedData;

      const successCount = processedData.length;
      const failedCount = failedData.length;
      const totalCount = successCount + failedCount;

      if (totalCount > 0) {
        window.$message?.success(`解析完成：成功 ${successCount} 条，失败 ${failedCount} 条`);
      } else {
        window.$message?.warning('未解析到有效数据');
      }
    } else {
      window.$message?.error('文件解析失败');
      successOrderData.value = [];
      failureOrderData.value = [];
      fullOrderData.value = [];
    }
  } catch (error) {
    console.error('上传文件失败:', error);
    window.$message?.error('上传文件失败');
    successOrderData.value = [];
    failureOrderData.value = [];
    fullOrderData.value = [];
  } finally {
    uploadLoading.value = false;
  }
}

// 创建订单表单数据
interface CreateOrderForm {
  asin_sku: string;
  buyer_name: string;
  order_amount: number | null;
  order_message: string;
  payment_time: number | null;
  platform_order_no: string;
  postal_code: string;
  product_id: string;
  product_image_url: string;
  product_name: string;
  product_price: number;
  purchase_url: string;
  product_quantity: number;
  product_specs: string;
  receiver_address: string;
  receiver_city: string;
  receiver_name: string;
  receiver_phone: string;
  receiver_state: string;
  remark: string;
  ship_deadline: number | null;
  shop_account: string;
}

const createOrderFormData = ref<CreateOrderForm>({
  asin_sku: '',
  buyer_name: '',
  order_amount: null,
  order_message: '',
  payment_time: null,
  platform_order_no: '',
  postal_code: '',
  product_id: '',
  product_image_url: '',
  purchase_url: '',
  product_name: '',
  product_price: 0,
  product_quantity: 1,
  product_specs: '',
  receiver_address: '',
  receiver_city: '',
  receiver_name: '',
  receiver_phone: '',
  receiver_state: '',
  remark: '',
  ship_deadline: null,
  shop_account: ''
});
// 提交物流单
async function handleSubmitLogistics() {
  if (!formData.value.logisticsNumber) {
    window.$message?.warning('请填写物流单号');
    return;
  }
  if (!formData.value.logisticsImage) {
    window.$message?.warning('请上传物流单图片');
    return;
  }

  if (!currentOrderId.value) {
    window.$message?.warning('请选择要上传物流单的订单');
    return;
  }

  try {
    // 调用上传物流单的API
    const { error } = await uploadLogistics(currentOrderId.value, {
      logistics_no: formData.value.logisticsNumber,
      image_paths: [formData.value.logisticsImage]
    });

    if (!error) {
      window.$message?.success('物流单上传成功');

      // 重置表单和订单ID
      formData.value = {
        logisticsNumber: '',
        logisticsImage: ''
      };
      currentOrderId.value = undefined;

      closeModal();
      // 刷新订单列表
      searchOrders();
    } else {
      window.$message?.error('物流单上传失败');
    }
  } catch (error) {
    console.error('上传物流单失败:', error);
    window.$message?.error('物流单上传失败');
  }
}

// 提交创建订单
async function handleSubmitCreateOrder() {
  // 验证必填字段
  if (!createOrderFormData.value.product_name) {
    window.$message?.warning('请填写商品名称');
    return;
  }
  if (!createOrderFormData.value.order_amount) {
    window.$message?.warning('请填写订单金额');
    return;
  }
  if (createOrderFormData.value.product_quantity <= 0) {
    window.$message?.warning('商品数量必须大于0');
    return;
  }

  try {
    // 处理日期时间字段，转换为字符串格式
    const submitData = {
      ...createOrderFormData.value,
      payment_time: createOrderFormData.value.payment_time
        ? new Date(createOrderFormData.value.payment_time).toISOString()
        : '',
      ship_deadline: createOrderFormData.value.ship_deadline
        ? new Date(createOrderFormData.value.ship_deadline).toISOString()
        : ''
    };

    const { error } = await createOrder(submitData);
    if (!error) {
      window.$message?.success('订单创建成功');
      // 重置表单
      resetCreateOrderForm();
      closeCreateOrderModal();
      // 刷新订单列表
      searchOrders();
    } else {
      window.$message?.error('创建订单失败');
    }
  } catch (error) {
    console.error('创建订单失败:', error);
    window.$message?.error('创建订单失败');
  }
}

// 重置创建订单表单
function resetCreateOrderForm() {
  createOrderFormData.value = {
    asin_sku: '',
    buyer_name: '',
    order_amount: null,
    order_message: '',
    payment_time: null,
    platform_order_no: '',
    postal_code: '',
    product_id: '',
    product_image_url: '',
    purchase_url: '',
    product_name: '',
    product_price: 0,
    product_quantity: 1,
    product_specs: '',
    receiver_address: '',
    receiver_city: '',
    receiver_name: '',
    receiver_phone: '',
    receiver_state: '',
    remark: '',
    ship_deadline: null,
    shop_account: ''
  };
}

// 刷新数据
function handleRefresh() {
  searchOrders();
}

async function handleConfirm() {
  if (fullOrderData.value.length === 0) {
    window.$message?.warning('暂无可提交的订单数据');
    return;
  }

  try {
    // 使用完整的解析数据进行提交
    const { data, error } = await importOrders(fullOrderData.value);
    console.log('data',data)
    if (!error) {
      if(data.failed_count){
        window.$message?.warning(`提交失败${data.failed_count}条`);
      }
      if(data.success_count){
        window.$message?.success(`提交成功${data.success_count}条`);
      }
      closeBatchUploadDrawer();
      // 清空数据
      successOrderData.value = [];
      failureOrderData.value = [];
      fullOrderData.value = [];
      orderFileList.value = [];
      // 提交成功后刷新订单列表
      searchOrders();
    } else {
      window.$message?.error('提交失败');
    }
  } catch (e) {
    console.error('提交失败:', e);
    window.$message?.error('提交失败');
  }
}
// 页面初始化
onMounted(() => {
  getOrderList();
});
</script>

<template>
  <div ref="orderManagement" class="relative p-16px">
    <!-- 标签页 -->
    <NTabs v-model:value="activeTab" type="line" class="mb-16px" @update:value="handleTabChange">
      <NTabPane v-for="tab in tabOptions" :key="tab.key" :name="tab.key" :tab="tab.label" />
    </NTabs>

    <!-- 搜索表单 -->
    <NCard :bordered="false" class="mb-16px">
      <div class="mb-16px flex items-center justify-between">
        <h3 class="text-16px font-medium">筛选条件</h3>
        <NButton text @click="toggleSearchExpanded">
          <template #icon>
            <SvgIcon :icon="searchExpanded ? 'mdi:chevron-up' : 'mdi:chevron-down'" />
          </template>
          {{ searchExpanded ? '收起' : '展开' }}
        </NButton>
      </div>

      <NForm
        v-show="searchExpanded"
        :model="searchForm"
        label-placement="left"
        label-width="140px"
        :show-feedback="false"
      >
        <NGrid :cols="3" :x-gap="16" :y-gap="16">
          <!-- 时间筛选 -->
          <NFormItemGi label="订单提交时间">
            <NDatePicker
              v-model:value="searchForm.orderSubmitTimeRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="客户付款时间（中国）">
            <NDatePicker
              v-model:value="searchForm.customerPaymentTimeRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>

          <NFormItemGi label="截止发货时间（中国）">
            <NDatePicker
              v-model:value="searchForm.deliveryDeadlineRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="预计到货时间（中国）">
            <NDatePicker
              v-model:value="searchForm.expectedArrivalChinaRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>

          <NFormItemGi label="预计到货时间（美国）">
            <NDatePicker
              v-model:value="searchForm.expectedArrivalUSRange"
              type="datetimerange"
              placeholder="请选择时间范围"
              clearable
            />
          </NFormItemGi>

          <!-- 输入框筛选 -->
          <NFormItemGi label="商品名称">
            <NInput v-model:value="searchForm.productName" placeholder="请输入商品名称" clearable />
          </NFormItemGi>
          <NFormItemGi label="代拍商品ASIM">
            <NInput v-model:value="searchForm.proxyProductASIM" placeholder="请输入代拍商品ASIM" clearable />
          </NFormItemGi>

          <NFormItemGi label="经营平台订单号">
            <NInput
              v-model:value="searchForm.businessPlatformOrderNumber"
              placeholder="请输入经营平台订单号"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="运单号">
            <NInput v-model:value="searchForm.trackingNumber" placeholder="请输入运单号" clearable />
          </NFormItemGi>

          <!-- <NFormItemGi label="速峰订单号">
            <NInput v-model:value="searchForm.sufengOrderNumber" placeholder="请输入速峰订单号" clearable />
          </NFormItemGi> -->
          <NFormItemGi label="备注">
            <NInput v-model:value="searchForm.orderMessage" placeholder="请输入备注" clearable />
          </NFormItemGi>

          <!-- <NFormItemGi label="B单号">
            <NInput v-model:value="searchForm.bOrderNumber" placeholder="请输入B单号" clearable />
          </NFormItemGi> -->
          <NFormItemGi label="店铺名称">
            <NInput v-model:value="searchForm.storeName" placeholder="请输入店铺名称" clearable />
          </NFormItemGi>

          <!-- 选择器筛选 -->
          <!-- <NFormItemGi label="订单标签">
            <NSelect
              v-model:value="searchForm.orderTags"
              :options="orderTagOptions"
              placeholder="请选择订单标签"
              multiple
              clearable
              max-tag-count="responsive"
            />
          </NFormItemGi> -->
          <!-- <NFormItemGi label="是否有B单号">
            <NSelect
              v-model:value="searchForm.hasBOrderNumber"
              :options="hasBOrderNumberOptions"
              placeholder="请选择"
              clearable
            />
          </NFormItemGi> -->
        </NGrid>

        <!-- 操作按钮 -->
        <div class="mt-16px flex justify-end gap-12px">
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" @click="handleSearch">搜索</NButton>
        </div>
      </NForm>
    </NCard>

    <!-- 操作栏 -->
    <div class="mb-16px flex items-center justify-between">
      <div class="flex gap-12px">
        <NButton type="primary" @click="handleAddOrder">
          <template #icon>
            <SvgIcon icon="ic:round-add" />
          </template>
          新增订单
        </NButton>
        <NButton type="info" @click="handleBatchImport">
          <template #icon>
            <SvgIcon icon="mdi:upload" />
          </template>
          批量导入订单
        </NButton>
        <NButton type="warning" :disabled="selectedOrderIds.length === 0" @click="handleBatchSetTags">
          <template #icon>
            <SvgIcon icon="mdi:tag-multiple" />
          </template>
          批量设置标签 ({{ selectedOrderIds.length }})
        </NButton>
        <NButton type="error" :disabled="selectedOrderIds.length === 0" @click="handleBatchCancel">
          <template #icon>
            <SvgIcon icon="mdi:cancel" />
          </template>
          批量取消
        </NButton>
      </div>
      <div class="flex gap-12px">
        <NButton @click="handleExport">
          <template #icon>
            <SvgIcon icon="mdi:download" />
          </template>
          导出
        </NButton>
        <NButton @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          刷新
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NCard :bordered="false" class="card-wrapper">
      <NDataTable
        :columns="columns"
        :data="orderList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        size="small"
        class="sm:h-full"
        scroll-x="1600"
        :row-key="(row: OrderData) => row.id || 0"
        @update:checked-row-keys="handleRowSelection"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </NCard>

    <!-- 新增订单弹出框 -->
    <!-- <NModal v-model:show="modalVisible" preset="card" title="新增订单" class="w-800px" :segmented="{ footer: 'soft' }">
      <div class="py-40px text-center">
        <NText depth="3">新增订单功能开发中...</NText>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="closeModal">确认</NButton>
        </div>
      </template>
    </NModal> -->

    <!-- 批量导入弹出框 -->
    <NModal
      v-model:show="importModalVisible"
      preset="card"
      title="批量导入订单"
      class="w-600px"
      :segmented="{ footer: 'soft' }"
    >
      <div class="py-40px text-center">
        <NText depth="3">批量导入功能开发中...</NText>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeImportModal">
            <template #icon>
              <SvgIcon icon="mdi:close" />
            </template>
            取消
          </NButton>
          <NButton type="primary" @click="closeImportModal">
            <template #icon>
              <SvgIcon icon="mdi:check" />
            </template>
            确认
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 批量设置标签弹出框 -->
    <NModal
      v-model:show="batchTagModalVisible"
      preset="card"
      title="批量设置标签"
      class="w-600px"
      :segmented="{ footer: 'soft' }"
    >
      <div class="mb-16px">
        <NText>已选择 {{ selectedOrderIds.length }} 个订单</NText>
      </div>

      <NFormItem label="选择标签">
        <NSelect
          v-model:value="batchSelectedTags"
          :options="orderTagOptions"
          placeholder="请选择要设置的标签"
          multiple
          clearable
          max-tag-count="responsive"
        >
          <template #header>
            <div class="flex gap-8px p-8px">
              <NButton size="small" @click="batchSelectedTags = orderTagOptions.map(o => o.value)">全选</NButton>
              <NButton size="small" @click="batchSelectedTags = []">清空</NButton>
              <NButton
                size="small"
                @click="
                  batchSelectedTags = orderTagOptions
                    .filter(o => !batchSelectedTags.includes(o.value))
                    .map(o => o.value)
                "
              >
                反选
              </NButton>
            </div>
          </template>
        </NSelect>
      </NFormItem>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeBatchTagModal">
            <template #icon>
              <SvgIcon icon="mdi:close" />
            </template>
            取消
          </NButton>
          <NButton type="primary" @click="handleConfirmBatchTags">
            <template #icon>
              <SvgIcon icon="mdi:check" />
            </template>
            确认设置
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 批量上传订单抽屉 -->
    <NDrawer
      v-model:show="batchUploadDrawerVisible"
      width="100%"
      placement="right"
      :show-mask="false"
      :mask-closable="false"
    >
      <NDrawerContent title="批量上传订单" closable>
        <div class="flex flex-col">
          <!-- 顶部操作区 -->
          <div class="mb-16px flex items-center justify-between">
            <div class="flex items-center gap-12px">
              <NUpload
                v-model:file-list="orderFileList"
                :max="1"
                accept=".xlsx,.xls"
                :show-file-list="true"
                @update:file-list="handleOrderFileUpload"
              >
                <NButton type="primary" :loading="uploadLoading">
                  <template #icon>
                    <SvgIcon icon="mdi:upload" />
                  </template>
                  上传订单文件
                </NButton>
              </NUpload>
              <NButton type="success" @click="handleManualCreateOrder">
                <template #icon>
                  <SvgIcon icon="ic:round-add" />
                </template>
                手动新建订单
              </NButton>
              <NButton @click="handleDownloadTemplate">
                <template #icon>
                  <SvgIcon icon="mdi:download" />
                </template>
                下载模板
              </NButton>
            </div>
            <div class="flex items-center gap-12px">
              <NButton type="success">
                <template #icon>
                  <SvgIcon icon="mdi:download" />
                </template>
                导出记录
              </NButton>
            </div>
          </div>

          <!-- 标签页 -->
          <NTabs v-model:value="batchUploadActiveTab" type="line" class="flex-1">
            <NTabPane name="success" tab="识别成功">
              <div class="h-full">
                <NDataTable
                  :columns="successColumns"
                  :data="successOrderData"
                  :bordered="false"
                  size="small"
                  :pagination="{ pageSize: 20 }"
                  class="h-full"
                />
              </div>
            </NTabPane>
            <NTabPane name="failure" tab="识别失败">
              <div class="h-full">
                <NDataTable
                  :columns="failureColumns"
                  :data="failureOrderData"
                  :bordered="false"
                  size="small"
                  :pagination="{ pageSize: 20 }"
                  class="h-full"
                />
              </div>
            </NTabPane>
          </NTabs>
        </div>
        <template #footer>
          <div class="flex justify-end gap-12px">
            <NButton @click="closeBatchUploadDrawer">
              <template #icon>
                <SvgIcon icon="mdi:close" />
              </template>
              取消
            </NButton>
            <NButton type="primary" @click="handleConfirm">
              <template #icon>
                <SvgIcon icon="mdi:check" />
              </template>
              提交
            </NButton>
          </div>
        </template>
      </NDrawerContent>
    </NDrawer>
    <!-- 拍单弹出框 -->
    <NModal
      v-model:show="purchaseModalVisible"
      preset="card"
      title="上传"
      class="w-600px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm :model="purchaseFormData" label-placement="left" label-width="120px" require-mark-placement="right-hanging">
        <NFormItem label="支付截图" required>
          <NUpload
            v-model:file-list="purchaseFileList"
            :max="1"
            list-type="image-card"
            accept="image/*"
            @update:file-list="handlePurchaseImageChange"
          >
            <NButton>上传截图</NButton>
          </NUpload>
        </NFormItem>
        <NFormItem label="商品价格" required>
          <NInputNumber
            v-model:value="purchaseFormData.productPrice"
            placeholder="请输入商品价格"
            class="w-full"
            :precision="2"
            :min="0"
          >
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closePurchaseModal">
            <template #icon>
              <SvgIcon icon="mdi:close" />
            </template>
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmitPurchase">
            <template #icon>
              <SvgIcon icon="mdi:check" />
            </template>
            确认拍单
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 上传物流单弹出框 -->
    <NModal
      v-model:show="modalVisible"
      preset="card"
      title="上传物流单"
      class="w-500px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm :model="formData" label-placement="left" label-width="120px" require-mark-placement="right-hanging">
        <NFormItem label="物流单号" required>
          <NInput v-model:value="formData.logisticsNumber" placeholder="请输入物流单号" clearable />
        </NFormItem>
        <NFormItem label="物流单图片" required>
          <NUpload
            v-model:file-list="logisticsFileList"
            :max="1"
            list-type="image-card"
            accept="image/*"
            @update:file-list="handleLogisticsImageChange"
          >
            <NButton>上传图片</NButton>
          </NUpload>
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">
            <template #icon>
              <SvgIcon icon="mdi:close" />
            </template>
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmitLogistics">
            <template #icon>
              <SvgIcon icon="mdi:check" />
            </template>
            确认提交
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 手动新建订单弹出框 -->
    <NModal
      v-model:show="createOrderModalVisible"
      preset="card"
      title="手动新建订单"
      class="w-1000px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm
        :model="createOrderFormData"
        label-placement="left"
        label-width="120px"
        require-mark-placement="right-hanging"
      >
        <NGrid :cols="2" :x-gap="16" :y-gap="16">
          <!-- 商品信息 -->
          <NFormItemGi label="商品名称" required>
            <NInput v-model:value="createOrderFormData.product_name" placeholder="请输入商品名称" clearable />
          </NFormItemGi>
          <NFormItemGi label="ASIN/SKU">
            <NInput v-model:value="createOrderFormData.asin_sku" placeholder="请输入ASIN/SKU" clearable />
          </NFormItemGi>
          <NFormItemGi label="商品ID">
            <NInput v-model:value="createOrderFormData.product_id" placeholder="请输入商品ID" clearable />
          </NFormItemGi>
          <NFormItemGi label="商品规格">
            <NInput v-model:value="createOrderFormData.product_specs" placeholder="请输入商品规格" clearable />
          </NFormItemGi>
          <NFormItemGi label="商品价格">
            <NInputNumber
              v-model:value="createOrderFormData.product_price"
              placeholder="请输入商品价格"
              class="w-full"
              :precision="2"
              :min="0"
            >
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItemGi>
          <NFormItemGi label="商品数量" required>
            <NInputNumber
              v-model:value="createOrderFormData.product_quantity"
              placeholder="请输入商品数量"
              class="w-full"
              :min="1"
            />
          </NFormItemGi>
          <NFormItemGi label="商品图片链接" required>
            <NInput v-model:value="createOrderFormData.product_image_url" placeholder="请输入商品图片链接" clearable />
          </NFormItemGi>
          <NFormItemGi label="代拍商品链接" required>
            <NInput v-model:value="createOrderFormData.purchase_url" placeholder="请输入代拍商品链接" clearable />
          </NFormItemGi>

          <!-- 订单信息 -->
          <NFormItemGi label="平台订单号" required>
            <NInput v-model:value="createOrderFormData.platform_order_no" placeholder="请输入平台订单号" clearable />
          </NFormItemGi>
          <NFormItemGi label="订单金额">
            <NInputNumber
              v-model:value="createOrderFormData.order_amount"
              placeholder="请输入订单金额"
              class="w-full"
              :precision="2"
              :min="0"
            >
              <template #suffix>元</template>
            </NInputNumber>
          </NFormItemGi>
          <NFormItemGi label="付款时间">
            <NDatePicker
              v-model:value="createOrderFormData.payment_time"
              type="datetime"
              placeholder="请选择付款时间"
              class="w-full"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="发货截止时间">
            <NDatePicker
              v-model:value="createOrderFormData.ship_deadline"
              type="datetime"
              placeholder="请选择发货截止时间"
              class="w-full"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="店铺账号">
            <NInput v-model:value="createOrderFormData.shop_account" placeholder="请输入店铺账号" clearable />
          </NFormItemGi>

          <!-- 买家信息 -->
          <NFormItemGi label="买家姓名">
            <NInput v-model:value="createOrderFormData.buyer_name" placeholder="请输入买家姓名" clearable />
          </NFormItemGi>

          <!-- 收货人信息 -->
          <NFormItemGi label="收货人姓名" required>
            <NInput v-model:value="createOrderFormData.receiver_name" placeholder="请输入收货人姓名" clearable />
          </NFormItemGi>
          <NFormItemGi label="收货人电话" required>
            <NInput v-model:value="createOrderFormData.receiver_phone" placeholder="请输入收货人电话" clearable />
          </NFormItemGi>
          <NFormItemGi label="收货人地址" required>
            <NInput v-model:value="createOrderFormData.receiver_address" placeholder="请输入收货人地址" clearable />
          </NFormItemGi>
          <NFormItemGi label="收货人城市" required>
            <NInput v-model:value="createOrderFormData.receiver_city" placeholder="请输入收货人城市" clearable />
          </NFormItemGi>
          <NFormItemGi label="收货人州/省" required>
            <NInput v-model:value="createOrderFormData.receiver_state" placeholder="请输入收货人州/省" clearable />
          </NFormItemGi>
          <NFormItemGi label="邮政编码" required>
            <NInput v-model:value="createOrderFormData.postal_code" placeholder="请输入邮政编码" clearable />
          </NFormItemGi>

          <NFormItemGi label="备注" :span="2">
            <NInput
              v-model:value="createOrderFormData.remark"
              type="textarea"
              placeholder="请输入备注"
              :rows="3"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="订单留言" :span="2">
            <NInput
              v-model:value="createOrderFormData.order_message"
              type="textarea"
              placeholder="请输入订单留言"
              :rows="3"
              clearable
            />
          </NFormItemGi>
        </NGrid>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeCreateOrderModal">
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmitCreateOrder">
            确认创建
          </NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style lang="scss">
.card-wrapper {
  min-height: 500px;
}
</style>
