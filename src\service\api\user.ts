import { request } from '../request';

/**
 * 获取用户列表
 *
 * @param params 查询参数
 */
export function fetchUserList(params?: Api.User.GetUserListParams) {
  return request<Api.User.GetUserListResponse>({
    url: '/api/users',
    method: 'get',
    params
  });
}

/**
 * 获取用户详情
 *
 * @param id 用户ID
 */
export function fetchUserDetail(id: number) {
  return request<Api.User.User>({
    url: `/api/users/${id}`,
    method: 'get'
  });
}

/**
 * 创建用户
 *
 * @param data 用户数据
 */
export function createUser(data: Api.User.CreateUserRequest) {
  return request<Api.User.Response<Api.User.User>>({
    url: '/api/users',
    method: 'post',
    data
  });
}

/**
 * 更新用户
 *
 * @param id 用户ID
 * @param data 用户数据
 */
export function updateUser(id: number, data: Api.User.UpdateUserRequest) {
  return request<Api.User.Response<Api.User.User>>({
    url: `/api/users/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除用户
 *
 * @param id 用户ID
 */
export function deleteUser(id: number) {
  return request<Api.User.Response<null>>({
    url: `/api/users/${id}`,
    method: 'delete'
  });
}

/**
 * 批量删除用户
 *
 * @param ids 用户ID列表
 */
export function batchDeleteUsers(ids: number[]) {
  return request<Api.User.Response<null>>({
    url: '/api/users/batch-delete',
    method: 'delete',
    data: { ids }
  });
}

/**
 * 更新用户状态
 *
 * @param id 用户ID
 * @param status 新状态
 */
export function updateUserStatus(id: number, status: Api.User.UserStatus) {
  return request<Api.User.Response<null>>({
    url: `/api/users/${id}/status`,
    method: 'put',
    data: { status }
  });
}

/**
 * 批量更新用户状态
 *
 * @param ids 用户ID列表
 * @param status 新状态
 */
export function batchUpdateUserStatus(ids: number[], status: Api.User.UserStatus) {
  return request<Api.User.Response<null>>({
    url: '/api/users/batch-status',
    method: 'put',
    data: { ids, status }
  });
}

/**
 * 重置用户密码
 *
 * @param id 用户ID
 * @param newPassword 新密码
 */
export function resetUserPassword(id: number, newPassword: string) {
  return request<Api.User.Response<null>>({
    url: `/api/users/${id}/reset-password`,
    method: 'put',
    data: { password: newPassword }
  });
}
