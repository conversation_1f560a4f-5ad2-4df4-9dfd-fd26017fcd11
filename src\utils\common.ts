import { $t } from '@/locales';
import { getSign, uploadToCDN } from '@/service/api/upload';

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as CommonType.Option<keyof T, T[keyof T]>[];
}

/**
 * Translate options
 *
 * @param options
 */
export function translateOptions(options: CommonType.Option<string, App.I18n.I18nKey>[]) {
  return options.map(option => ({
    ...option,
    label: $t(option.label)
  }));
}

/**
 * Toggle html class
 *
 * @param className
 */
export function toggleHtmlClass(className: string) {
  function add() {
    document.documentElement.classList.add(className);
  }

  function remove() {
    document.documentElement.classList.remove(className);
  }

  return {
    add,
    remove
  };
}

// 上传图片 获取auth，Policy
/*
 scene 场景值，区分上传文件的根路径
 type  类型值，区分上传业务bucket
 name 文件名称
 status: 1 测试， 2正式
*/
export enum UploadWay {
  TEST = 1,
  PROD = 2,
}

async function getSecret(scene: string, name: string = '', status = UploadWay.TEST) {
  try {
    // const SAVE_PATH = name ? `/${scene}/${name}` : `/${scene}/{filemd5}{day}{hour}{min}{sec}{.suffix}`
    const SAVE_PATH = `/${scene}/{filemd5}/${name}`;

    const params = {
      method: 'post',
      save_key: SAVE_PATH,
      status,
    };

    // 获取签名
    const { data, error } = await getSign(params);

    if (!error && data) {
      return data;
    } else {
      throw new Error('获取签名失败');
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '获取签名失败');
  }
}
/**
 * 上传文件到CDN
 * @param file 传入文件
 * @param scene 传入场景值，默认 'product'
 * @param name 文件名称，默认为空
 * @param status 上传状态：1 测试，2 正式
 * @param onProgress 上传进度回调函数
 * @returns Promise
 */
export async function uploadCDNImg(
  file: File,
  scene: string = 'product',
  name: string = '',
  status = UploadWay.TEST,
  onProgress?: (progress: number) => void,
): Promise<any> {
  try {
    // 获取签名信息
    const result = await getSecret(scene, name, status);

    if (!result) {
      throw new Error('获取签名失败');
    }

    // 显示上传提示
    window.$message?.info('上传中...');

    // 上传文件到CDN
    const { data, error } = await uploadToCDN(
      file,
      result.bucket,
      result.authorization,
      result.policy,
      onProgress
    );

    if (!error && data) {
      window.$message?.success('上传成功');
      return data;
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    window.$message?.error(errorMessage);
    throw new Error(errorMessage);
  }
}
