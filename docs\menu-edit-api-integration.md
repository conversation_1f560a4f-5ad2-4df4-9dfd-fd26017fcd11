# 编辑菜单接口对接文档

## 概述

本文档记录了编辑菜单接口的对接过程，包括API接口定义、服务函数实现和前端页面功能完善。

## 接口信息

- **接口地址**: `PUT /api/menus/{id}`
- **请求方式**: PUT
- **Content-Type**: application/json

### 请求参数

```typescript
interface UpdateMenuRequest {
  code?: string;
  component?: string;
  description?: string;
  hidden?: boolean;
  icon?: string;
  keep_alive?: boolean;
  name?: string;
  parent_id?: number;
  path?: string;
  permission?: string;
  redirect?: string;
  sort?: number;
  status?: number;
  type?: number;
  [property: string]: any;
}
```

### 示例请求

```bash
curl --location --request PUT 'http://************:8080/api/menus/{id}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "code": "user_management",
    "component": "view.system_user",
    "description": "用户管理页面",
    "hidden": false,
    "icon": "mdi:account-group",
    "keep_alive": true,
    "name": "用户管理",
    "parent_id": 1,
    "path": "/system/user",
    "permission": "system:user:view",
    "redirect": "",
    "sort": 1,
    "status": 1,
    "type": 1
}'
```

## 实现内容

### 1. API类型定义 (src/typings/api.d.ts)

添加了菜单相关的接口类型：

```typescript
namespace Menu {
  /** 菜单树节点 */
  interface Response {
    children?: Api.Menu.Response[];
    code?: string;
    component?: string;
    created_at?: string;
    description?: string;
    hidden?: boolean;
    icon?: string;
    id?: number;
    keep_alive?: boolean;
    level?: number;
    name?: string;
    parent_id?: number;
    path?: string;
    permission?: string;
    redirect?: string;
    sort?: number;
    status?: number;
    type?: number;
    updated_at?: string;
    [property: string]: any;
  }

  /** 更新菜单请求参数 */
  interface UpdateMenuRequest {
    code?: string;
    component?: string;
    description?: string;
    hidden?: boolean;
    icon?: string;
    keep_alive?: boolean;
    name?: string;
    parent_id?: number;
    path?: string;
    permission?: string;
    redirect?: string;
    sort?: number;
    status?: number;
    type?: number;
    [property: string]: any;
  }

  /** 创建菜单请求参数 */
  interface CreateMenuRequest {
    code: string;
    component?: string;
    description?: string;
    hidden?: boolean;
    icon?: string;
    keep_alive?: boolean;
    name: string;
    parent_id?: number;
    path?: string;
    permission?: string;
    redirect?: string;
    sort?: number;
    status?: number;
    type: number;
  }
}
```

### 2. API服务层 (src/service/api/menu.ts)

扩展了菜单API服务，添加了完整的CRUD操作：

```typescript
/**
 * 获取菜单列表
 */
export function fetchMenuList() {
  return request<Api.Menu.Response[]>({
    url: '/api/menus',
    method: 'get'
  });
}

/**
 * 获取菜单详情
 */
export function fetchMenuDetail(id: number) {
  return request<Api.Menu.Response>({
    url: `/api/menus/${id}`,
    method: 'get'
  });
}

/**
 * 创建菜单
 */
export function createMenu(data: Api.Menu.CreateMenuRequest) {
  return request<Api.Menu.Response>({
    url: '/api/menus',
    method: 'post',
    data
  });
}

/**
 * 更新菜单
 */
export function updateMenu(id: number, data: Api.Menu.UpdateMenuRequest) {
  return request<Api.Menu.Response>({
    url: `/api/menus/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除菜单
 */
export function deleteMenu(id: number) {
  return request<any>({
    url: `/api/menus/${id}`,
    method: 'delete'
  });
}

/**
 * 批量删除菜单
 */
export function batchDeleteMenus(ids: number[]) {
  return request<any>({
    url: '/api/menus/batch-delete',
    method: 'delete',
    data: { ids }
  });
}
```

### 3. 前端页面实现 (src/views/system/menu/index.vue)

#### 主要功能模块

**状态管理**
```typescript
// 弹窗状态
const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean();
const isEdit = ref(false);
const currentMenuId = ref<number | undefined>();

// 菜单表单数据
const menuFormData = reactive<Api.Menu.UpdateMenuRequest>({
  code: '',
  name: '',
  path: '',
  component: '',
  icon: '',
  permission: '',
  description: '',
  parent_id: 0,
  type: 1,
  status: 1,
  sort: 0,
  hidden: false,
  keep_alive: true,
  redirect: ''
});
```

**核心功能实现**

1. **新增菜单**
```typescript
const handleAdd = () => {
  isEdit.value = false;
  currentMenuId.value = undefined;
  resetForm();
  openModal();
};
```

2. **编辑菜单**
```typescript
const handleEdit = (row: Api.Menu.Response) => {
  isEdit.value = true;
  currentMenuId.value = row.id;
  Object.assign(menuFormData, {
    code: row.code || '',
    name: row.name || '',
    // ... 其他字段
  });
  openModal();
};
```

3. **提交菜单表单**
```typescript
const handleSubmitMenu = async () => {
  if (!menuFormData.name) {
    message.warning('请输入菜单名称');
    return;
  }
  if (!menuFormData.code) {
    message.warning('请输入菜单编码');
    return;
  }

  try {
    if (isEdit.value && currentMenuId.value) {
      // 编辑菜单
      const { error } = await updateMenu(currentMenuId.value, menuFormData);
      if (!error) {
        message.success('菜单更新成功');
        closeModal();
        getMenuTree();
      }
    } else {
      // 新增菜单
      const { error } = await createMenu(menuFormData as Api.Menu.CreateMenuRequest);
      if (!error) {
        message.success('菜单创建成功');
        closeModal();
        getMenuTree();
      }
    }
  } catch (error) {
    console.error('提交菜单表单失败:', error);
    message.error('操作失败');
  }
};
```

4. **删除菜单**
```typescript
const handleDelete = async (row: Api.Menu.Response) => {
  window.$dialog?.warning({
    title: '确认删除',
    content: `确定要删除菜单"${row.name}"吗？`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const { error } = await deleteMenu(row.id!);
        if (!error) {
          message.success('菜单删除成功');
          getMenuTree();
        }
      } catch (error) {
        console.error('删除菜单失败:', error);
        message.error('删除失败');
      }
    }
  });
};
```

#### 表单字段

**基本信息**
- 菜单名称（必填）
- 菜单编码（必填）
- 菜单路径
- 组件路径
- 菜单图标
- 权限标识

**配置信息**
- 父级菜单ID
- 菜单类型（菜单/按钮）
- 状态（启用/禁用）
- 排序值
- 是否隐藏
- 是否缓存
- 重定向路径
- 菜单描述

## 功能特性

### 用户体验
- ✅ 响应式表单布局（2列网格）
- ✅ 完整的表单验证
- ✅ 操作确认对话框
- ✅ 实时反馈和提示
- ✅ 自动刷新数据

### 技术特性
- ✅ TypeScript类型安全
- ✅ 完整的错误处理
- ✅ 统一的API调用模式
- ✅ 组件化设计
- ✅ 状态管理

## API接口规范

| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取菜单树 | GET | `/api/menus/tree` | 获取树形菜单结构 |
| 获取菜单列表 | GET | `/api/menus` | 获取平铺菜单列表 |
| 获取菜单详情 | GET | `/api/menus/{id}` | 根据ID获取菜单信息 |
| 创建菜单 | POST | `/api/menus` | 创建新菜单 |
| 更新菜单 | PUT | `/api/menus/{id}` | 更新菜单信息 |
| 删除菜单 | DELETE | `/api/menus/{id}` | 删除单个菜单 |
| 批量删除菜单 | DELETE | `/api/menus/batch-delete` | 批量删除菜单 |

## 数据字段说明

- **code**: 菜单编码，用于权限控制
- **name**: 菜单名称，显示在界面上
- **path**: 路由路径
- **component**: Vue组件路径
- **icon**: 菜单图标
- **permission**: 权限标识
- **parent_id**: 父级菜单ID，0表示根菜单
- **type**: 菜单类型，1=菜单，2=按钮
- **status**: 状态，1=启用，0=禁用
- **sort**: 排序值，数字越小越靠前
- **hidden**: 是否隐藏菜单
- **keep_alive**: 是否缓存页面
- **redirect**: 重定向路径
- **description**: 菜单描述

## 项目状态

- ✅ **项目正常运行**: http://localhost:9527
- ✅ **菜单管理页面**: http://localhost:9527/system/menu
- ✅ **完整CRUD功能**: 新增、编辑、删除菜单
- ✅ **类型安全**: 通过TypeScript检查
- ✅ **错误处理**: 完整的异常处理机制

现在菜单管理功能已经完全可用，支持完整的菜单CRUD操作，包括树形结构显示、编辑表单和删除确认等功能。
