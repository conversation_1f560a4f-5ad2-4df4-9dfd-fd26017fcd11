import { request } from '../request';

/**
 * 获取菜单树
 */
export function fetchMenuTree() {
  return request<Api.Menu.Response[]>({
    url: '/api/menus/tree',
    method: 'get'
  });
}

/**
 * 获取菜单列表
 */
export function fetchMenuList() {
  return request<Api.Menu.Response[]>({
    url: '/api/menus',
    method: 'get'
  });
}

/**
 * 获取菜单详情
 *
 * @param id 菜单ID
 */
export function fetchMenuDetail(id: number) {
  return request<Api.Menu.Response>({
    url: `/api/menus/${id}`,
    method: 'get'
  });
}

/**
 * 创建菜单
 *
 * @param data 菜单数据
 */
export function createMenu(data: Api.Menu.CreateMenuRequest) {
  return request<Api.Menu.Response>({
    url: '/api/menus',
    method: 'post',
    data
  });
}

/**
 * 更新菜单
 *
 * @param id 菜单ID
 * @param data 菜单数据
 */
export function updateMenu(id: number, data: Api.Menu.UpdateMenuRequest) {
  return request<Api.Menu.Response>({
    url: `/api/menus/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除菜单
 *
 * @param id 菜单ID
 */
export function deleteMenu(id: number) {
  return request<any>({
    url: `/api/menus/${id}`,
    method: 'delete'
  });
}

/**
 * 获取用户菜单权限
 *
 * @param userId 用户ID
 */
export function fetchUserMenus(userId: number) {
  return request<{
    code: number;
    message: string;
    data: Api.Menu.Response[];
  }>({
    url: `/api/menus/user/${userId}`,
    method: 'get'
  });
}

/**
 * 获取当前用户菜单权限
 * 如果不传用户ID，则获取当前登录用户的菜单权限
 */
export function fetchCurrentUserMenus() {
  return request<{
    code: number;
    message: string;
    data: Api.Menu.Response[];
  }>({
    url: '/api/menus/user/current',
    method: 'get'
  });
}
