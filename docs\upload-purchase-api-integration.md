# 上传拍单信息接口对接文档

## 概述

本文档记录了上传拍单信息接口的对接过程，包括API接口定义、类型声明和前端实现。

## 接口信息

- **接口地址**: `PUT /api/orders/{orderId}/purchase`
- **请求方式**: PUT
- **Content-Type**: application/json

### 请求参数

```typescript
interface UploadPurchaseInfoRequest {
  /** 拍单截图路径数组 */
  image_paths: string[];
  /** 拍单金额 */
  purchase_amount: number;
}
```

### 示例请求

```bash
curl --location --request PUT 'http://************:8080/api/orders/{orderId}/purchase' \
--header 'Authorization: Bearer {token}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "image_paths": [
        "/uploads/purchase/screenshot1.jpg"
    ],
    "purchase_amount": 99.99
}'
```

## 实现内容

### 1. 类型定义 (src/typings/api.d.ts)

添加了上传拍单信息的请求参数类型：

```typescript
/** 上传拍单信息请求参数 */
interface UploadPurchaseInfoRequest {
  /** 拍单截图路径数组 */
  image_paths: string[];
  /** 拍单金额 */
  purchase_amount: number;
}
```

### 2. API服务 (src/service/api/order.ts)

添加了上传拍单信息的API函数：

```typescript
/**
 * 上传拍单信息
 *
 * @param orderId 订单ID
 * @param data 拍单信息数据
 */
export function uploadPurchaseInfo(orderId: number, data: Api.Order.UploadPurchaseInfoRequest) {
  return request<Api.Order.Response<null>>({
    url: `/api/orders/${orderId}/purchase`,
    method: 'put',
    data
  });
}
```

### 3. 前端实现 (src/views/workspace/order/index.vue)

#### 截图上传处理
修改了 `handlePurchaseImageChange` 函数，实现真实的图片上传：

```typescript
// 处理拍单截图上传
async function handlePurchaseImageChange(fileList: any[]) {
  if (fileList.length > 0) {
    const file = fileList[0].file;
    if (file) {
      try {
        const { data, error } = await uploadFile(file);
        if (!error && data) {
          purchaseFormData.value.productScreenshot = data.file_path;
        } else {
          window.$message?.error('截图上传失败');
          purchaseFormData.value.productScreenshot = '';
        }
      } catch (error) {
        console.error('上传截图失败:', error);
        window.$message?.error('截图上传失败');
        purchaseFormData.value.productScreenshot = '';
      }
    }
  } else {
    purchaseFormData.value.productScreenshot = '';
  }
}
```

#### 拍单信息提交处理
修改了 `handleSubmitPurchase` 函数，调用真实的API：

```typescript
// 提交拍单
async function handleSubmitPurchase() {
  if (!purchaseFormData.value.productScreenshot) {
    window.$message?.warning('请上传商品页面截图');
    return;
  }
  if (!purchaseFormData.value.productPrice) {
    window.$message?.warning('请填写商品价格');
    return;
  }
  if (!currentOrderId.value) {
    window.$message?.warning('请选择要拍单的订单');
    return;
  }

  try {
    // 调用上传拍单信息API
    const { error: uploadError } = await uploadPurchaseInfo(currentOrderId.value, {
      image_paths: [purchaseFormData.value.productScreenshot],
      purchase_amount: purchaseFormData.value.productPrice
    });

    if (!uploadError) {
      // 更新订单状态为拍单中
      const { error: statusError } = await updateOrderStatus(currentOrderId.value, 'processing');
      if (!statusError) {
        window.$message?.success('拍单提交成功');
        // 重置表单和刷新列表
        purchaseFormData.value = {
          productScreenshot: '',
          productPrice: null
        };
        currentOrderId.value = undefined;
        closePurchaseModal();
        searchOrders();
      } else {
        window.$message?.error('更新订单状态失败');
      }
    } else {
      window.$message?.error('拍单信息上传失败');
    }
  } catch (error) {
    console.error('拍单提交失败:', error);
    window.$message?.error('拍单提交失败');
  }
}
```

## 功能流程

1. 用户在订单列表中点击"拍单"按钮（仅对待拍单状态的订单显示）
2. 弹出拍单信息对话框
3. 用户上传支付截图（自动调用文件上传API）
4. 用户填写商品价格
5. 用户点击"确认拍单"
6. 调用上传拍单信息API
7. 成功后更新订单状态为"拍单中"
8. 关闭对话框并刷新订单列表

## 业务逻辑

- **触发条件**: 订单状态为"待拍单"(pending)时显示拍单按钮
- **提交后状态**: 订单状态更新为"拍单中"(processing)
- **必填字段**: 支付截图和商品价格
- **文件上传**: 使用通用文件上传API获取图片路径

## 注意事项

1. 拍单功能分为两个步骤：
   - 上传拍单信息（截图和价格）
   - 更新订单状态
2. 只有两个步骤都成功才算拍单成功
3. 截图上传使用了通用的文件上传API (`/api/upload`)
4. 所有API调用都包含了完整的错误处理
5. 成功提交后会自动刷新订单列表

## 测试建议

1. 测试截图上传功能是否正常
2. 测试商品价格验证（必须大于0）
3. 测试API调用的错误处理
4. 测试成功提交后的状态更新和界面刷新
5. 测试只有待拍单状态的订单才显示拍单按钮
