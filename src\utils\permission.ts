import { ref, computed } from 'vue';
import { fetchCurrentUserMenus } from '@/service/api/menu';

// 用户菜单权限数据
const userMenus = ref<Api.Menu.Response[]>([]);
const userPermissions = ref<string[]>([]);

/**
 * 获取用户菜单权限
 */
export async function getUserMenuPermissions() {
  try {
    const { data, error } = await fetchCurrentUserMenus();
    if (!error && data?.data) {
      userMenus.value = data.data;
      // 提取所有权限标识
      userPermissions.value = extractPermissions(data.data);
      return data.data;
    }
    return [];
  } catch (error) {
    console.error('获取用户菜单权限失败:', error);
    return [];
  }
}

/**
 * 从菜单树中提取所有权限标识
 */
function extractPermissions(menus: Api.Menu.Response[]): string[] {
  const permissions: string[] = [];
  
  function traverse(menuList: Api.Menu.Response[]) {
    menuList.forEach(menu => {
      if (menu.permission) {
        permissions.push(menu.permission);
      }
      if (menu.children && menu.children.length > 0) {
        traverse(menu.children);
      }
    });
  }
  
  traverse(menus);
  return permissions;
}

/**
 * 检查用户是否有指定权限
 */
export function hasPermission(permission: string): boolean {
  return userPermissions.value.includes(permission);
}

/**
 * 检查用户是否有任意一个权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  return permissions.some(permission => hasPermission(permission));
}

/**
 * 检查用户是否有所有权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  return permissions.every(permission => hasPermission(permission));
}

/**
 * 过滤用户有权限的菜单
 */
export function filterMenusByPermission(menus: Api.Menu.Response[]): Api.Menu.Response[] {
  return menus.filter(menu => {
    // 如果是菜单类型且没有权限标识，或者有权限且用户拥有该权限
    const hasMenuPermission = !menu.permission || hasPermission(menu.permission);
    
    if (hasMenuPermission) {
      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = filterMenusByPermission(menu.children);
      }
      return true;
    }
    
    return false;
  });
}

/**
 * 获取用户可访问的路由菜单
 */
export function getAccessibleRoutes(): Api.Menu.Response[] {
  return filterMenusByPermission(userMenus.value);
}

/**
 * 转换菜单数据为路由格式
 */
export function transformMenusToRoutes(menus: Api.Menu.Response[]) {
  return menus
    .filter(menu => menu.type === 1 && !menu.hidden) // 只要菜单类型且不隐藏的
    .map(menu => ({
      name: menu.code,
      path: menu.path,
      component: menu.component,
      meta: {
        title: menu.name,
        icon: menu.icon,
        permission: menu.permission,
        keepAlive: menu.keep_alive,
        hidden: menu.hidden,
        sort: menu.sort
      },
      children: menu.children ? transformMenusToRoutes(menu.children) : undefined
    }));
}

/**
 * 权限指令 - 用于v-permission指令
 */
export function checkPermissionDirective(el: HTMLElement, permission: string | string[]) {
  let hasAuth = false;
  
  if (Array.isArray(permission)) {
    hasAuth = hasAnyPermission(permission);
  } else {
    hasAuth = hasPermission(permission);
  }
  
  if (!hasAuth) {
    el.style.display = 'none';
  } else {
    el.style.display = '';
  }
}

/**
 * 权限组合式函数
 */
export function usePermission() {
  return {
    userMenus: computed(() => userMenus.value),
    userPermissions: computed(() => userPermissions.value),
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getUserMenuPermissions,
    getAccessibleRoutes,
    filterMenusByPermission
  };
}

// 导出权限相关的响应式数据
export { userMenus, userPermissions };
