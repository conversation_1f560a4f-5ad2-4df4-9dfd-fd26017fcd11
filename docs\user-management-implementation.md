# 用户管理页面开发文档

## 概述

本文档记录了用户管理页面的完整开发过程，包括API接口定义、页面组件实现和路由配置。

## 功能特性

### 核心功能
- ✅ **用户列表展示**：分页显示用户列表，包含用户名、手机号、角色、状态、创建时间等字段
- ✅ **新增用户**：弹窗表单支持添加新用户，包含完整的表单验证
- ✅ **编辑用户**：支持修改用户信息（用户名、手机号、角色、状态等）
- ✅ **删除用户**：支持单个删除和批量删除，包含确认对话框
- ✅ **搜索筛选**：支持按用户名、手机号、角色、状态等条件搜索
- ✅ **状态管理**：支持启用/禁用用户状态切换
- ✅ **密码重置**：支持重置用户密码功能

### 技术特性
- ✅ 使用项目现有的API架构模式
- ✅ 完整的TypeScript类型定义
- ✅ NaiveUI组件库构建界面
- ✅ 遵循项目代码规范和架构模式
- ✅ 完整的错误处理和用户提示

## 实现内容

### 1. API类型定义 (src/typings/api.d.ts)

```typescript
namespace Api.User {
  /** 用户角色枚举 */
  enum UserRole {
    AUser = 'a_user',    // 普通用户
    Admin = 'admin',     // 管理员
    Purchaser = 'purchaser' // 采购员
  }

  /** 用户状态枚举 */
  enum UserStatus {
    Active = 1,    // 启用
    Inactive = 0   // 禁用
  }

  /** 用户信息 */
  interface User {
    id?: number;
    username: string;
    phone: string;
    password?: string;
    role: UserRole;
    status: UserStatus;
    created_at?: string;
    updated_at?: string;
  }

  /** 获取用户列表请求参数 */
  interface GetUserListParams {
    page?: number;
    page_size?: number;
    username?: string;
    phone?: string;
    role?: UserRole;
    status?: UserStatus;
  }

  /** 获取用户列表响应数据 */
  interface GetUserListResponse {
    list?: User[];
    page?: number;
    page_size?: number;
    total?: number;
  }

  /** 创建用户请求参数 */
  interface CreateUserRequest {
    username: string;
    phone: string;
    password: string;
    role: UserRole;
    status: UserStatus;
  }

  /** 更新用户请求参数 */
  interface UpdateUserRequest {
    username?: string;
    phone?: string;
    password?: string;
    role?: UserRole;
    status?: UserStatus;
  }
}
```

### 2. API服务层 (src/service/api/user.ts)

实现了完整的用户管理API函数：

- `fetchUserList()` - 获取用户列表
- `fetchUserDetail()` - 获取用户详情
- `createUser()` - 创建用户
- `updateUser()` - 更新用户
- `deleteUser()` - 删除用户
- `batchDeleteUsers()` - 批量删除用户
- `updateUserStatus()` - 更新用户状态
- `batchUpdateUserStatus()` - 批量更新用户状态
- `resetUserPassword()` - 重置用户密码

### 3. 页面组件 (src/views/workspace/user-management/index.vue)

#### 主要功能模块

**搜索筛选区域**
- 用户名搜索
- 手机号搜索
- 角色筛选
- 状态筛选
- 重置和搜索按钮

**操作工具栏**
- 新增用户按钮
- 批量删除按钮（显示选中数量）
- 刷新按钮

**数据表格**
- 支持多选
- 分页显示
- 角色和状态标签显示
- 操作列：编辑、重置密码、启用/禁用、删除

**弹窗组件**
- 新增/编辑用户弹窗
- 重置密码弹窗

#### 状态管理
```typescript
// 数据状态
const loading = ref(false);
const userList = ref<UserData[]>([]);
const selectedUserIds = ref<number[]>([]);

// 表单状态
const userFormData = ref<UserForm>({...});
const resetPasswordForm = ref({...});

// 弹窗状态
const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean();
const { bool: resetPasswordModalVisible, ... } = useBoolean();
```

#### 核心功能实现

**用户列表获取**
```typescript
async function getUserList() {
  loading.value = true;
  try {
    const params: Api.User.GetUserListParams = {
      page: pagination.page,
      page_size: pagination.pageSize,
      username: searchForm.username || undefined,
      phone: searchForm.phone || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status !== '' ? searchForm.status : undefined
    };

    const { data, error } = await fetchUserList(params);
    if (!error && data) {
      userList.value = data.list || [];
      pagination.total = data.total || 0;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    window.$message?.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
}
```

**用户创建/编辑**
```typescript
async function handleSubmitUser() {
  // 表单验证
  if (!userFormData.value.username) {
    window.$message?.warning('请输入用户名');
    return;
  }

  try {
    if (isEdit.value && currentUserId.value) {
      // 编辑用户
      const { error } = await updateUser(currentUserId.value, updateData);
      if (!error) {
        window.$message?.success('用户更新成功');
        closeModal();
        getUserList();
      }
    } else {
      // 新增用户
      const { error } = await createUser(userFormData.value);
      if (!error) {
        window.$message?.success('用户创建成功');
        closeModal();
        getUserList();
      }
    }
  } catch (error) {
    console.error('提交用户表单失败:', error);
    window.$message?.error('操作失败');
  }
}
```

### 4. 路由配置

路由已自动配置在 `src/router/elegant/routes.ts`：

```typescript
{
  name: 'workspace_user-management',
  path: '/workspace/user-management',
  component: 'view.workspace_user-management',
  meta: {
    title: 'workspace_user-management',
    i18nKey: 'route.workspace_user-management'
  }
}
```

## 接口规范

### 用户管理API接口

| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取用户列表 | GET | `/api/users` | 支持分页和筛选 |
| 获取用户详情 | GET | `/api/users/{id}` | 根据ID获取用户信息 |
| 创建用户 | POST | `/api/users` | 创建新用户 |
| 更新用户 | PUT | `/api/users/{id}` | 更新用户信息 |
| 删除用户 | DELETE | `/api/users/{id}` | 删除单个用户 |
| 批量删除用户 | DELETE | `/api/users/batch-delete` | 批量删除用户 |
| 更新用户状态 | PUT | `/api/users/{id}/status` | 启用/禁用用户 |
| 批量更新状态 | PUT | `/api/users/batch-status` | 批量更新用户状态 |
| 重置密码 | PUT | `/api/users/{id}/reset-password` | 重置用户密码 |

## 数据字段

### 用户信息字段
- **id**: 用户ID（自动生成）
- **username**: 用户名（必填，唯一）
- **phone**: 手机号（必填，唯一）
- **password**: 密码（创建时必填，编辑时可选）
- **role**: 角色（a_user/admin/purchaser）
- **status**: 状态（1=启用，0=禁用）
- **created_at**: 创建时间（自动生成）
- **updated_at**: 更新时间（自动更新）

## 用户体验特性

### 交互设计
- ✅ 响应式布局，适配不同屏幕尺寸
- ✅ 加载状态显示，提升用户体验
- ✅ 操作确认对话框，防止误操作
- ✅ 实时搜索和筛选功能
- ✅ 批量操作支持，提高效率

### 错误处理
- ✅ 网络请求错误处理
- ✅ 表单验证错误提示
- ✅ 业务逻辑错误处理
- ✅ 用户友好的错误消息

### 性能优化
- ✅ 分页加载，避免一次性加载大量数据
- ✅ 防抖搜索，减少不必要的API调用
- ✅ 组件懒加载，提升页面加载速度

## 安全考虑

### 权限控制
- 用户角色权限验证
- 操作权限检查
- 敏感操作确认

### 数据安全
- 密码加密存储
- 输入数据验证
- XSS防护

## 扩展建议

### 功能扩展
1. **用户导入导出**：支持Excel批量导入用户
2. **用户头像**：支持用户头像上传和显示
3. **操作日志**：记录用户管理操作日志
4. **权限管理**：细粒度的权限控制

### 性能优化
1. **虚拟滚动**：大数据量时使用虚拟滚动
2. **缓存机制**：添加数据缓存机制
3. **搜索优化**：添加搜索建议和历史记录

## 总结

用户管理页面已完全按照项目架构和代码规范实现，具备完整的CRUD功能和良好的用户体验。所有代码都经过TypeScript类型检查，确保了类型安全和代码质量。

页面访问路径：`/workspace/user-management`

项目继续在 http://localhost:9527 正常运行，可以直接访问和测试用户管理功能。
