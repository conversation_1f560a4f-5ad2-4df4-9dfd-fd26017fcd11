import { useAuthStore } from '@/store/modules/auth';

export function useAuth() {
  const authStore = useAuthStore();

  function hasAuth(codes: string | string[]) {
    if (!authStore.isLogin) {
      return false;
    }

    // 基于用户角色判断权限
    const userRole = authStore.userInfo.role;

    // 管理员拥有所有权限
    if (userRole === 'admin') {
      return true;
    }

    // 根据角色和权限码进行判断
    if (typeof codes === 'string') {
      return hasPermissionByRole(userRole, codes);
    }

    return codes.some(code => hasPermissionByRole(userRole, code));
  }

  function hasPermissionByRole(role: string | undefined, code: string): boolean {
    if (!role) return false;

    // 这里可以根据实际业务需求定义角色权限映射
    const rolePermissions: Record<string, string[]> = {
      admin: ['*'], // 管理员拥有所有权限
      purchaser: ['purchase', 'view'], // 采购员权限
      a_user: ['view'] // 普通用户权限
    };

    const permissions = rolePermissions[role] || [];
    return permissions.includes('*') || permissions.includes(code);
  }

  return {
    hasAuth
  };
}
