import { request } from '../request';

/**
 * 获取订单列表
 *
 * @param params 查询参数
 */
export function fetchOrderList(params?: Api.Order.GetOrderListParams) {
  return request<Api.Order.GetOrderListResponse>({
    url: '/api/orders',
    method: 'get',
    params
  });
}

/**
 * 获取订单详情
 *
 * @param id 订单ID
 */
export function fetchOrderDetail(id: number) {
  return request<Api.Order.Order>({
    url: `/api/orders/${id}`,
    method: 'get'
  });
}

/**
 * 删除订单
 *
 * @param id 订单ID
 */
export function deleteOrder(id: number) {
  return request({
    url: `/api/orders/${id}`,
    method: 'delete'
  });
}

/**
 * 取消订单
 *
 * @param id 订单ID
 */
export function cancelOrder(id: number) {
  return request({
    url: `/api/orders/${id}/status`,
    method: 'put',
    data: {
      id: id.toString(),
      status: 'cancelled'
    }
  });
}

/**
 * 更新订单状态
 *
 * @param id 订单ID
 * @param status 新状态
 */
export function updateOrderStatus(id: number, status: string) {
  return request({
    url: `/api/orders/${id}/status`,
    method: 'put',
    data: { status }
  });
}

/**
 * 创建订单
 *
 * @param data 订单数据
 */
export function createOrder(data: Partial<Api.Order.Order>) {
  return request<Api.Order.Response<Api.Order.Order>>({
    url: '/api/orders',
    method: 'post',
    data
  });
}

/**
 * 更新订单
 *
 * @param id 订单ID
 * @param data 订单数据
 */
export function updateOrder(id: number, data: Partial<Api.Order.Order>) {
  return request<Api.Order.Response<Api.Order.Order>>({
    url: `/api/orders/${id}`,
    method: 'put',
    data
  });
}



/**
 * 批量更新订单状态
 *
 * @param ids 订单ID列表
 * @param status 新状态
 */
export function batchUpdateOrderStatus(ids: number[], status: Api.Order.OrderStatus) {
  return request<Api.Order.Response<null>>({
    url: '/api/orders/batch-status',
    method: 'put',
    data: { ids, status }
  });
}

/**
 * 下载订单导入模板
 */
export function downloadTemplate() {
  return request<Blob, 'blob'>({
    url: '/api/excel/template',
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 上传并解析Excel订单文件
 *
 * @param file Excel文件
 */
export function uploadOrderExcel(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<Api.Order.ExcelParseResponse>({
    url: '/api/excel/parse',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 批量导入订单
 *
 * @param orders 解析后的订单数据列表
 */
export function importOrders(orders: Api.Order.ExcelParseItem[]) {
  return request<Api.Order.ImportResult>({
    url: '/api/excel/import-orders',
    method: 'post',
    data: orders,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 上传文件
 *
 * @param file 文件
 */
export function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<{ file_path: string }>({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 上传物流单
 *
 * @param orderId 订单ID
 * @param data 物流单数据
 */
export function uploadLogistics(orderId: number, data: Api.Order.UploadLogisticsRequest) {
  return request<Api.Order.Response<null>>({
    url: `/api/orders/${orderId}/logistics`,
    method: 'put',
    data
  });
}

/**
 * 上传拍单信息
 *
 * @param orderId 订单ID
 * @param data 拍单信息数据
 */
export function uploadPurchaseInfo(orderId: number, data: Api.Order.UploadPurchaseInfoRequest) {
  return request<Api.Order.Response<null>>({
    url: `/api/orders/${orderId}/purchase`,
    method: 'put',
    data
  });
}
