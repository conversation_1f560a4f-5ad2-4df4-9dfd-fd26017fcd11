import { request } from '../request';

/**
 * 获取CDN上传签名
 *
 * @param params 签名参数
 */
export function getSign(params: {
  method: string;
  save_key: string;
  status: number;
}) {
  return request<{
    authorization: string;
    policy: string;
    bucket: string;
  }>({
    url: '/v1/cdn/token',
    method: 'get',
    params
  });
}

/**
 * 上传文件到CDN
 *
 * @param file 文件
 * @param bucket CDN bucket
 * @param authorization 授权信息
 * @param policy 策略信息
 * @param onProgress 进度回调
 */
export function uploadToCDN(
  file: File,
  bucket: string,
  authorization: string,
  policy: string,
  onProgress?: (progress: number) => void
) {
  const formData = new FormData();
  formData.append('authorization', authorization);
  formData.append('policy', policy);
  formData.append('file', file);

  return request<any>({
    url: `/${bucket}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted);
      }
    }
  });
}
