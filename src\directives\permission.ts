import type { Directive, DirectiveBinding } from 'vue';
import { hasPermission, hasAnyPermission } from '@/utils/permission';

/**
 * 权限指令
 * 用法：
 * v-permission="'system:user:add'" - 单个权限
 * v-permission="['system:user:add', 'system:user:edit']" - 多个权限（任意一个）
 * v-permission:all="['system:user:add', 'system:user:edit']" - 多个权限（全部）
 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  }
};

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value, arg } = binding;
  
  if (!value) {
    console.warn('v-permission指令需要传入权限值');
    return;
  }
  
  let hasAuth = false;
  
  if (Array.isArray(value)) {
    // 多个权限
    if (arg === 'all') {
      // 需要全部权限
      hasAuth = value.every(permission => hasPermission(permission));
    } else {
      // 任意一个权限即可
      hasAuth = hasAnyPermission(value);
    }
  } else {
    // 单个权限
    hasAuth = hasPermission(value);
  }
  
  if (!hasAuth) {
    // 没有权限则隐藏元素
    el.style.display = 'none';
    el.setAttribute('disabled', 'true');
  } else {
    // 有权限则显示元素
    el.style.display = '';
    el.removeAttribute('disabled');
  }
}

/**
 * 权限检查函数 - 用于组件内部逻辑判断
 */
export function checkAuth(permission: string | string[], requireAll = false): boolean {
  if (Array.isArray(permission)) {
    return requireAll 
      ? permission.every(p => hasPermission(p))
      : hasAnyPermission(permission);
  }
  return hasPermission(permission);
}

export default permission;
