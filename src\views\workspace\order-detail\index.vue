<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NCard, NDescriptions, NDescriptionsItem, NTag, NButton, NSpace, NSpin, NText, useMessage } from 'naive-ui';
import { fetchOrderDetail } from '@/service/api';
import { useOrderStatus } from '@/hooks/business/order';

defineOptions({
  name: 'OrderDetail'
});

const route = useRoute();
const router = useRouter();
const message = useMessage();
const { getStatusInfo } = useOrderStatus();

// 订单详情数据
const orderDetail = ref<Api.Order.Order | null>(null);
const loading = ref(false);

// 获取订单ID
const orderId = computed(() => {
  const id = route.params.id || route.query.id;
  return id ? Number(id) : null;
});

// 获取订单详情
const getOrderDetail = async () => {
  if (!orderId.value) {
    message.error('订单ID不能为空');
    return;
  }

  try {
    loading.value = true;
    const { data, error } = await fetchOrderDetail(orderId.value);
    
    if (!error && data) {
      orderDetail.value = data;
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    message.error('获取订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回订单列表
const handleBack = () => {
  router.push('/workspace/order');
};

// 编辑订单
const handleEdit = () => {
  if (orderDetail.value?.id) {
    message.info(`编辑订单功能开发中... 订单ID: ${orderDetail.value.id}`);
  }
};

// 格式化时间
const formatTime = (time?: string) => {
  return time ? new Date(time).toLocaleString() : '-';
};

// 格式化金额
const formatAmount = (amount?: number) => {
  return amount ? `¥${amount.toFixed(2)}` : '-';
};

// 页面初始化
onMounted(() => {
  getOrderDetail();
});
</script>

<template>
  <div class="h-full p-16px">
    <NSpin :show="loading">
      <!-- 页面标题和操作按钮 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-12px">
          <NButton @click="handleBack">
            <template #icon>
              <icon-mdi-arrow-left />
            </template>
            返回
          </NButton>
          <h2 class="text-18px font-bold">订单详情</h2>
        </div>
        
        <NSpace>
          <NButton type="primary" @click="handleEdit">
            编辑订单
          </NButton>
        </NSpace>
      </div>

      <div v-if="orderDetail" class="grid grid-cols-1 gap-16px">
        <!-- 基本信息 -->
        <NCard title="基本信息" :bordered="false">
          <NDescriptions :column="3" label-placement="left">
            <NDescriptionsItem label="订单号">
              {{ orderDetail.order_no || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="平台订单号">
              {{ orderDetail.platform_order_no || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="订单状态">
              <NTag :type="getStatusInfo(orderDetail.status || '').type as any" size="small">
                {{ getStatusInfo(orderDetail.status || '').text }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="订单金额">
              {{ formatAmount(orderDetail.order_amount) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="创建时间">
              {{ formatTime(orderDetail.created_at) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="付款时间">
              {{ formatTime(orderDetail.payment_time) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="发货截止时间">
              {{ formatTime(orderDetail.ship_deadline) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="店铺账号">
              {{ orderDetail.shop_account || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="备注">
              {{ orderDetail.remark || '-' }}
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- 买家信息 -->
        <NCard title="买家信息" :bordered="false">
          <NDescriptions :column="3" label-placement="left">
            <NDescriptionsItem label="买家姓名">
              {{ orderDetail.buyer_name || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="收货人姓名">
              {{ orderDetail.receiver_name || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="收货人电话">
              {{ orderDetail.receiver_phone || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="收货地址" :span="3">
              {{ orderDetail.receiver_address || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="收货城市">
              {{ orderDetail.receiver_city || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="收货州/省">
              {{ orderDetail.receiver_state || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="邮政编码">
              {{ orderDetail.postal_code || '-' }}
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- 商品信息 -->
        <NCard title="商品信息" :bordered="false">
          <NDescriptions :column="3" label-placement="left">
            <NDescriptionsItem label="商品ID">
              {{ orderDetail.product_id || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="商品名称">
              {{ orderDetail.product_name || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="ASIN/SKU">
              {{ orderDetail.asin_sku || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="商品价格">
              {{ formatAmount(orderDetail.product_price) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="商品数量">
              {{ orderDetail.product_quantity || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="商品规格">
              {{ orderDetail.product_specs || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="商品图片" :span="3">
              <img 
                v-if="orderDetail.product_image_url" 
                :src="orderDetail.product_image_url" 
                alt="商品图片"
                class="max-w-200px max-h-200px object-contain"
              />
              <span v-else>-</span>
            </NDescriptionsItem>
            <NDescriptionsItem label="订单留言" :span="3">
              {{ orderDetail.order_message || '-' }}
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- 采购信息 -->
        <NCard v-if="orderDetail.purchase_record" title="采购信息" :bordered="false">
          <NDescriptions :column="3" label-placement="left">
            <NDescriptionsItem label="B单号">
              {{ orderDetail.purchase_record.b_order_no || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="采购金额">
              {{ formatAmount(orderDetail.purchase_record.purchase_amount) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="物流单号">
              {{ orderDetail.purchase_record.logistics_no || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="采购员">
              {{ orderDetail.purchase_record.purchaser?.username || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="采购状态">
              <NTag :type="getStatusInfo(orderDetail.purchase_record.status || '').type as any" size="small">
                {{ getStatusInfo(orderDetail.purchase_record.status || '').text }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="采购时间">
              {{ formatTime(orderDetail.purchase_record.created_at) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="采购备注" :span="3">
              {{ orderDetail.purchase_record.remark || '-' }}
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- 用户信息 -->
        <!-- <NCard v-if="orderDetail.user" title="用户信息" :bordered="false">
          <NDescriptions :column="3" label-placement="left">
            <NDescriptionsItem label="用户名">
              {{ orderDetail.user.username || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="手机号">
              {{ orderDetail.user.phone || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="用户角色">
              {{ orderDetail.user.role || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="用户状态">
              <NTag :type="orderDetail.user.status === 1 ? 'success' : 'error'" size="small">
                {{ orderDetail.user.status === 1 ? '正常' : '禁用' }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="注册时间">
              {{ formatTime(orderDetail.user.created_at) }}
            </NDescriptionsItem>
          </NDescriptions>
        </NCard> -->
      </div>

      <!-- 数据为空时的提示 -->
      <div v-else-if="!loading" class="text-center py-40px">
        <NText depth="3">暂无订单详情数据</NText>
      </div>
    </NSpin>
  </div>
</template>

<style lang="scss">

</style>
