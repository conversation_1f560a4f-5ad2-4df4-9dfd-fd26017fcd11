/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      hideInMenu: true,
      constant: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      hideInMenu: true,
      constant: true
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard'
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      hideInMenu: true,
      constant: true,
      i18nKey: 'route.iframe-page'
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      hideInMenu: true,
      constant: true
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system'
    },
    children: [
      {
        name: 'system_menu',
        path: '/system/menu',
        component: 'view.system_menu',
        meta: {
          title: 'system_menu',
          i18nKey: 'route.system_menu'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      }
    ]
  },
  {
    name: 'workspace',
    path: '/workspace',
    component: 'layout.base',
    meta: {
      title: 'workspace',
      i18nKey: 'route.workspace'
    },
    children: [
      {
        name: 'workspace_order',
        path: '/workspace/order',
        component: 'view.workspace_order',
        meta: {
          title: 'workspace_order',
          i18nKey: 'route.workspace_order'
        }
      },
      {
        name: 'workspace_order-detail',
        path: '/workspace/order-detail',
        component: 'view.workspace_order-detail',
        meta: {
          title: 'workspace_order-detail',
          i18nKey: 'route.workspace_order-detail',
          hideInMenu: true
        }
      }
    ]
  }
];
