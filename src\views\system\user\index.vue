<script lang="ts" setup>
import { h, reactive, ref, onMounted } from 'vue';
import { useBoolean } from '@sa/hooks';
import { NButton, NTag, NPopconfirm } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { 
  fetchUserList, 
  createUser, 
  updateUser, 
  deleteUser, 
  batchDeleteUsers,
  updateUserStatus,
  batchUpdateUserStatus,
  resetUserPassword
} from '@/service/api/user';

defineOptions({
  name: 'UserManagement'
});

// 用户角色枚举
enum UserRole {
  AUser = 'a_user',
  Admin = 'admin',
  Purchaser = 'purchaser'
}

// 用户状态枚举
enum UserStatus {
  Active = 1,
  Inactive = 0
}

// 用户数据类型
interface UserData {
  id?: number;
  username: string;
  phone: string;
  password?: string;
  role: UserRole;
  status: UserStatus;
  created_at?: string;
  updated_at?: string;
}

// 搜索表单数据
interface SearchForm {
  username: string;
  phone: string;
  role: UserRole | '';
  status: UserStatus | '';
}

const searchForm = reactive<SearchForm>({
  username: '',
  phone: '',
  role: '',
  status: ''
});

// 用户表单数据
interface UserForm {
  username: string;
  phone: string;
  password: string;
  role: UserRole;
  status: UserStatus;
}

const userFormData = ref<UserForm>({
  username: '',
  phone: '',
  password: '',
  role: UserRole.AUser,
  status: UserStatus.Active
});

// 状态管理
const loading = ref(false);
const userList = ref<UserData[]>([]);
const selectedUserIds = ref<number[]>([]);
const currentUserId = ref<number | undefined>();
const isEdit = ref(false);

// 弹窗状态
const { bool: modalVisible, setTrue: openModal, setFalse: closeModal } = useBoolean();
const { bool: resetPasswordModalVisible, setTrue: openResetPasswordModal, setFalse: closeResetPasswordModal } = useBoolean();

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount} 条`
});

// 角色选项
const roleOptions = [
  { label: '全部', value: '' },
  { label: '普通用户', value: UserRole.AUser },
  { label: '管理员', value: UserRole.Admin },
  { label: '采购员', value: UserRole.Purchaser }
];

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: UserStatus.Active },
  { label: '禁用', value: UserStatus.Inactive }
];

// 重置密码表单
const resetPasswordForm = ref({
  newPassword: '',
  confirmPassword: ''
});

// 获取角色标签类型
function getRoleTagType(role: UserRole) {
  const roleMap = {
    [UserRole.AUser]: 'default',
    [UserRole.Admin]: 'error',
    [UserRole.Purchaser]: 'warning'
  };
  return roleMap[role] || 'default';
}

// 获取角色文本
function getRoleText(role: UserRole) {
  const roleMap = {
    [UserRole.AUser]: '普通用户',
    [UserRole.Admin]: '管理员',
    [UserRole.Purchaser]: '采购员'
  };
  return roleMap[role] || '未知';
}

// 获取状态标签类型
function getStatusTagType(status: UserStatus) {
  return status === UserStatus.Active ? 'success' : 'error';
}

// 获取状态文本
function getStatusText(status: UserStatus) {
  return status === UserStatus.Active ? '启用' : '禁用';
}

// 表格列定义
const columns = [
  {
    type: 'selection' as const,
    width: 50
  },
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '用户名',
    key: 'username',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '手机号',
    key: 'phone',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '角色',
    key: 'role',
    width: 120,
    render: (row: UserData) => {
      return h(NTag, { type: getRoleTagType(row.role) as any, size: 'small' }, {
        default: () => getRoleText(row.role)
      });
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: UserData) => {
      return h(NTag, { type: getStatusTagType(row.status) as any, size: 'small' }, {
        default: () => getStatusText(row.status)
      });
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render: (row: UserData) => {
      return row.created_at ? new Date(row.created_at).toLocaleString() : '-';
    }
  },
  {
    title: '操作',
    key: 'operate',
    width: 280,
    fixed: 'right' as const,
    render: (row: UserData) => {
      const buttons = [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row)
          },
          { default: () => '编辑' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'warning',
            onClick: () => handleResetPassword(row.id!)
          },
          { default: () => '重置密码' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: row.status === UserStatus.Active ? 'error' : 'success',
            onClick: () => handleToggleStatus(row.id!, row.status)
          },
          { default: () => row.status === UserStatus.Active ? '禁用' : '启用' }
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete(row.id!)
          },
          {
            default: () => '确定删除此用户吗？',
            trigger: () => h(
              NButton,
              {
                size: 'small',
                type: 'error'
              },
              { default: () => '删除' }
            )
          }
        )
      ];

      return h('div', { class: 'flex gap-8px flex-wrap' }, buttons);
    }
  }
];

// 获取用户列表
async function getUserList() {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      username: searchForm.username || undefined,
      phone: searchForm.phone || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status !== '' ? searchForm.status : undefined
    };

    const { data, error } = await fetchUserList(params);
    if (!error && data) {
      userList.value = data.list || [];
      pagination.total = data.total || 0;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    window.$message?.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1;
  getUserList();
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    username: '',
    phone: '',
    role: '',
    status: ''
  });
  pagination.page = 1;
  getUserList();
}

// 新增用户
function handleAdd() {
  isEdit.value = false;
  currentUserId.value = undefined;
  Object.assign(userFormData.value, {
    username: '',
    phone: '',
    password: '',
    role: UserRole.AUser,
    status: UserStatus.Active
  });
  openModal();
}

// 编辑用户
function handleEdit(user: UserData) {
  isEdit.value = true;
  currentUserId.value = user.id;
  Object.assign(userFormData.value, {
    username: user.username,
    phone: user.phone,
    password: '',
    role: user.role,
    status: user.status
  });
  openModal();
}

// 删除用户
async function handleDelete(id: number) {
  try {
    const { error } = await deleteUser(id);
    if (!error) {
      window.$message?.success('删除成功');
      getUserList();
    } else {
      window.$message?.error('删除失败');
    }
  } catch (error) {
    console.error('删除用户失败:', error);
    window.$message?.error('删除失败');
  }
}

// 批量删除
async function handleBatchDelete() {
  if (selectedUserIds.value.length === 0) {
    window.$message?.warning('请选择要删除的用户');
    return;
  }

  window.$dialog?.warning({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedUserIds.value.length} 个用户吗？`,
    positiveText: '确认删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const { error } = await batchDeleteUsers(selectedUserIds.value);
        if (!error) {
          window.$message?.success(`成功删除 ${selectedUserIds.value.length} 个用户`);
          selectedUserIds.value = [];
          getUserList();
        } else {
          window.$message?.error('批量删除失败');
        }
      } catch (error) {
        console.error('批量删除用户失败:', error);
        window.$message?.error('批量删除失败');
      }
    }
  });
}

// 切换用户状态
async function handleToggleStatus(id: number, currentStatus: UserStatus) {
  const newStatus = currentStatus === UserStatus.Active
    ? UserStatus.Inactive
    : UserStatus.Active;
  
  try {
    const { error } = await updateUserStatus(id, newStatus);
    if (!error) {
      window.$message?.success('状态更新成功');
      getUserList();
    } else {
      window.$message?.error('状态更新失败');
    }
  } catch (error) {
    console.error('更新用户状态失败:', error);
    window.$message?.error('状态更新失败');
  }
}

// 重置密码
function handleResetPassword(id: number) {
  currentUserId.value = id;
  resetPasswordForm.value = {
    newPassword: '',
    confirmPassword: ''
  };
  openResetPasswordModal();
}

// 提交重置密码
async function handleSubmitResetPassword() {
  if (!resetPasswordForm.value.newPassword) {
    window.$message?.warning('请输入新密码');
    return;
  }
  if (resetPasswordForm.value.newPassword !== resetPasswordForm.value.confirmPassword) {
    window.$message?.warning('两次输入的密码不一致');
    return;
  }
  if (!currentUserId.value) {
    window.$message?.warning('用户ID不能为空');
    return;
  }

  try {
    const { error } = await resetUserPassword(currentUserId.value, resetPasswordForm.value.newPassword);
    if (!error) {
      window.$message?.success('密码重置成功');
      closeResetPasswordModal();
    } else {
      window.$message?.error('密码重置失败');
    }
  } catch (error) {
    console.error('重置密码失败:', error);
    window.$message?.error('密码重置失败');
  }
}

// 提交用户表单
async function handleSubmitUser() {
  if (!userFormData.value.username) {
    window.$message?.warning('请输入用户名');
    return;
  }
  if (!userFormData.value.phone) {
    window.$message?.warning('请输入手机号');
    return;
  }
  if (!isEdit.value && !userFormData.value.password) {
    window.$message?.warning('请输入密码');
    return;
  }

  try {
    if (isEdit.value && currentUserId.value) {
      // 编辑用户
      const updateData = {
        username: userFormData.value.username,
        phone: userFormData.value.phone,
        role: userFormData.value.role,
        status: userFormData.value.status,
        ...(userFormData.value.password && { password: userFormData.value.password })
      };
      
      const { error } = await updateUser(currentUserId.value, updateData);
      if (!error) {
        window.$message?.success('用户更新成功');
        closeModal();
        getUserList();
      } else {
        window.$message?.error('用户更新失败');
      }
    } else {
      // 新增用户
      const { error } = await createUser(userFormData.value);
      if (!error) {
        window.$message?.success('用户创建成功');
        closeModal();
        getUserList();
      } else {
        window.$message?.error('用户创建失败');
      }
    }
  } catch (error) {
    console.error('提交用户表单失败:', error);
    window.$message?.error('操作失败');
  }
}

// 表格行选择
function handleRowSelection(rowKeys: Array<string | number>) {
  selectedUserIds.value = rowKeys.map(key => Number(key));
}

// 分页处理
function handlePageChange(page: number) {
  pagination.page = page;
  getUserList();
}

function handlePageSizeChange(pageSize: number) {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  getUserList();
}

// 刷新数据
function handleRefresh() {
  getUserList();
}

// 页面初始化
onMounted(() => {
  getUserList();
});
</script>

<template>
  <div class="relative p-16px">
    <!-- 搜索表单 -->
    <NCard :bordered="false" class="mb-16px">
      <NForm
        :model="searchForm"
        label-placement="left"
        label-width="80px"
        :show-feedback="false"
      >
        <NGrid :cols="4" :x-gap="16" :y-gap="16">
          <NFormItemGi label="用户名">
            <NInput v-model:value="searchForm.username" placeholder="请输入用户名" clearable />
          </NFormItemGi>
          <NFormItemGi label="手机号">
            <NInput v-model:value="searchForm.phone" placeholder="请输入手机号" clearable />
          </NFormItemGi>
          <NFormItemGi label="角色">
            <NSelect
              v-model:value="searchForm.role"
              :options="roleOptions"
              placeholder="请选择角色"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="状态">
            <NSelect
              v-model:value="searchForm.status"
              :options="statusOptions"
              placeholder="请选择状态"
              clearable
            />
          </NFormItemGi>
        </NGrid>

        <!-- 操作按钮 -->
        <div class="mt-16px flex justify-end gap-12px">
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" @click="handleSearch">搜索</NButton>
        </div>
      </NForm>
    </NCard>

    <!-- 操作栏 -->
    <div class="mb-16px flex items-center justify-between">
      <div class="flex gap-12px">
        <NButton type="primary" @click="handleAdd">
          <template #icon>
            <SvgIcon icon="ic:round-add" />
          </template>
          新增用户
        </NButton>
        <NButton type="error" :disabled="selectedUserIds.length === 0" @click="handleBatchDelete">
          <template #icon>
            <SvgIcon icon="mdi:delete" />
          </template>
          批量删除 ({{ selectedUserIds.length }})
        </NButton>
      </div>
      <div class="flex gap-12px">
        <NButton @click="handleRefresh">
          <template #icon>
            <SvgIcon icon="mdi:refresh" />
          </template>
          刷新
        </NButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <NCard :bordered="false" class="card-wrapper">
      <NDataTable
        :columns="columns"
        :data="userList"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        size="small"
        class="sm:h-full"
        :row-key="(row: UserData) => row.id || 0"
        @update:checked-row-keys="handleRowSelection"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </NCard>

    <!-- 新增/编辑用户弹出框 -->
    <NModal
      v-model:show="modalVisible"
      preset="card"
      :title="isEdit ? '编辑用户' : '新增用户'"
      class="w-600px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm
        :model="userFormData"
        label-placement="left"
        label-width="80px"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="用户名" required>
          <NInput v-model:value="userFormData.username" placeholder="请输入用户名" clearable />
        </NFormItem>
        <NFormItem label="手机号" required>
          <NInput v-model:value="userFormData.phone" placeholder="请输入手机号" clearable />
        </NFormItem>
        <NFormItem :label="isEdit ? '新密码' : '密码'" :required="!isEdit">
          <NInput
            v-model:value="userFormData.password"
            type="password"
            :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'"
            clearable
          />
        </NFormItem>
        <NFormItem label="角色" required>
          <NSelect
            v-model:value="userFormData.role"
            :options="roleOptions.filter(item => item.value !== '')"
            placeholder="请选择角色"
          />
        </NFormItem>
        <NFormItem label="状态" required>
          <NSelect
            v-model:value="userFormData.status"
            :options="statusOptions.filter(item => item.value !== '')"
            placeholder="请选择状态"
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">
            <template #icon>
              <SvgIcon icon="mdi:close" />
            </template>
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmitUser">
            <template #icon>
              <SvgIcon icon="mdi:check" />
            </template>
            {{ isEdit ? '更新' : '创建' }}
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 重置密码弹出框 -->
    <NModal
      v-model:show="resetPasswordModalVisible"
      preset="card"
      title="重置密码"
      class="w-500px"
      :segmented="{ footer: 'soft' }"
    >
      <NForm
        :model="resetPasswordForm"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="新密码" required>
          <NInput
            v-model:value="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            clearable
          />
        </NFormItem>
        <NFormItem label="确认密码" required>
          <NInput
            v-model:value="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            clearable
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeResetPasswordModal">
            <template #icon>
              <SvgIcon icon="mdi:close" />
            </template>
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmitResetPassword">
            <template #icon>
              <SvgIcon icon="mdi:check" />
            </template>
            确认重置
          </NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style lang="scss">
.card-wrapper {
  min-height: 500px;
}
</style>
