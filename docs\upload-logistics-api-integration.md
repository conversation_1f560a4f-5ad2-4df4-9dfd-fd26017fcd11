# 上传物流单接口对接文档

## 概述

本文档记录了上传物流单接口的对接过程，包括API接口定义、类型声明和前端实现。

## 接口信息

- **接口地址**: `PUT /api/orders/{orderId}/logistics`
- **请求方式**: PUT
- **Content-Type**: application/json

### 请求参数

```typescript
interface UploadLogisticsRequest {
  /** 物流单图片路径数组 */
  image_paths: string[];
  /** 物流单号 */
  logistics_no: string;
}
```

### 示例请求

```bash
curl --location --request PUT 'http://************:8080/api/orders/{orderId}/logistics' \
--header 'Authorization: Bearer {token}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "image_paths": [
        "/uploads/logistics/image1.jpg"
    ],
    "logistics_no": "SF1234567890"
}'
```

## 实现内容

### 1. 类型定义 (src/typings/api.d.ts)

添加了上传物流单的请求参数类型：

```typescript
/** 上传物流单请求参数 */
interface UploadLogisticsRequest {
  /** 物流单图片路径数组 */
  image_paths: string[];
  /** 物流单号 */
  logistics_no: string;
}
```

### 2. API服务 (src/service/api/order.ts)

添加了两个新的API函数：

#### 文件上传API
```typescript
/**
 * 上传文件
 *
 * @param file 文件
 */
export function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<{ file_path: string }>({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
```

#### 上传物流单API
```typescript
/**
 * 上传物流单
 *
 * @param orderId 订单ID
 * @param data 物流单数据
 */
export function uploadLogistics(orderId: number, data: Api.Order.UploadLogisticsRequest) {
  return request<Api.Order.Response<null>>({
    url: `/api/orders/${orderId}/logistics`,
    method: 'put',
    data
  });
}
```

### 3. 前端实现 (src/views/workspace/order/index.vue)

#### 图片上传处理
修改了 `handleLogisticsImageChange` 函数，实现真实的图片上传：

```typescript
// 处理物流单图片上传
async function handleLogisticsImageChange(fileList: any[]) {
  if (fileList.length > 0) {
    const file = fileList[0].file;
    if (file) {
      try {
        const { data, error } = await uploadFile(file);
        if (!error && data) {
          formData.value.logisticsImage = data.file_path;
        } else {
          window.$message?.error('图片上传失败');
          formData.value.logisticsImage = '';
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        window.$message?.error('图片上传失败');
        formData.value.logisticsImage = '';
      }
    }
  } else {
    formData.value.logisticsImage = '';
  }
}
```

#### 物流单提交处理
修改了 `handleSubmitLogistics` 函数，调用真实的API：

```typescript
// 提交物流单
async function handleSubmitLogistics() {
  if (!formData.value.logisticsNumber) {
    window.$message?.warning('请填写物流单号');
    return;
  }
  if (!formData.value.logisticsImage) {
    window.$message?.warning('请上传物流单图片');
    return;
  }

  if (!currentOrderId.value) {
    window.$message?.warning('请选择要上传物流单的订单');
    return;
  }

  try {
    // 调用上传物流单的API
    const { error } = await uploadLogistics(currentOrderId.value, {
      logistics_no: formData.value.logisticsNumber,
      image_paths: [formData.value.logisticsImage]
    });

    if (!error) {
      window.$message?.success('物流单上传成功');
      // 重置表单和刷新列表
      formData.value = {
        logisticsNumber: '',
        logisticsImage: ''
      };
      currentOrderId.value = undefined;
      closeModal();
      searchOrders();
    } else {
      window.$message?.error('物流单上传失败');
    }
  } catch (error) {
    console.error('上传物流单失败:', error);
    window.$message?.error('物流单上传失败');
  }
}
```

## 功能流程

1. 用户在订单列表中点击"物流"按钮
2. 弹出上传物流单对话框
3. 用户填写物流单号
4. 用户上传物流单图片（自动调用文件上传API）
5. 用户点击"确认提交"
6. 调用上传物流单API
7. 成功后关闭对话框并刷新订单列表

## 注意事项

1. 图片上传使用了通用的文件上传API (`/api/upload`)
2. 上传物流单API需要订单ID作为路径参数
3. 所有API调用都包含了错误处理和用户提示
4. 成功上传后会自动刷新订单列表以显示最新状态

## 测试建议

1. 测试图片上传功能是否正常
2. 测试物流单号验证
3. 测试API调用的错误处理
4. 测试成功提交后的界面更新
