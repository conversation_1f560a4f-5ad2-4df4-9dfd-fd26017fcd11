# 用户菜单权限接口对接文档

## 概述

本文档记录了用户菜单权限接口的对接过程，包括API接口定义、权限管理工具和前端权限控制的完整实现。

## 接口信息

- **接口地址**: `GET /api/menus/user/{userId}`
- **请求方式**: GET
- **功能**: 获取指定用户的菜单权限

### 响应格式

```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "name": "系统管理",
            "code": "system",
            "path": "/system",
            "icon": "setting",
            "sort": 1,
            "parent_id": null,
            "level": 1,
            "type": 1,
            "status": 1,
            "component": "Layout",
            "permission": "",
            "redirect": "",
            "hidden": false,
            "keep_alive": false,
            "description": "系统管理模块",
            "created_at": "2025-08-08 18:59:54",
            "updated_at": "2025-08-08 18:59:54",
            "children": [...]
        }
    ]
}
```

## 实现内容

### 1. API类型定义 (src/typings/api.d.ts)

添加了用户菜单权限的响应类型：

```typescript
namespace Menu {
  /** 用户菜单权限响应 */
  interface UserMenuResponse {
    code: number;
    message: string;
    data: Response[];
  }
}
```

### 2. API服务层 (src/service/api/menu.ts)

添加了获取用户菜单权限的API函数：

```typescript
/**
 * 获取用户菜单权限
 *
 * @param userId 用户ID
 */
export function fetchUserMenus(userId: number) {
  return request<Api.Menu.UserMenuResponse>({
    url: `/api/menus/user/${userId}`,
    method: 'get'
  });
}

/**
 * 获取当前用户菜单权限
 */
export function fetchCurrentUserMenus() {
  return request<Api.Menu.UserMenuResponse>({
    url: '/api/menus/user/current',
    method: 'get'
  });
}
```

### 3. 权限管理工具 (src/utils/permission.ts)

创建了完整的权限管理工具：

#### 核心功能
- **权限获取**: `getUserMenuPermissions()` - 获取用户菜单权限
- **权限检查**: `hasPermission()` - 检查单个权限
- **批量权限检查**: `hasAnyPermission()`, `hasAllPermissions()`
- **菜单过滤**: `filterMenusByPermission()` - 过滤用户有权限的菜单
- **路由转换**: `transformMenusToRoutes()` - 转换菜单为路由格式

#### 使用示例
```typescript
import { getUserMenuPermissions, hasPermission } from '@/utils/permission';

// 获取用户权限
await getUserMenuPermissions();

// 检查权限
if (hasPermission('system:user:add')) {
  // 用户有新增用户权限
}
```

### 4. 权限指令 (src/directives/permission.ts)

创建了Vue指令用于模板中的权限控制：

```typescript
// 单个权限
v-permission="'system:user:add'"

// 多个权限（任意一个）
v-permission="['system:user:add', 'system:user:edit']"

// 多个权限（全部）
v-permission:all="['system:user:add', 'system:user:edit']"
```

### 5. 权限组合式函数 (src/hooks/useAuth.ts)

创建了便于在组件中使用的权限管理Hook：

```typescript
import { useAuth } from '@/hooks/useAuth';

export default {
  setup() {
    const { 
      hasPermission, 
      authState, 
      getButtonPermissions,
      filterTableActions 
    } = useAuth();
    
    return {
      hasPermission,
      authState
    };
  }
}
```

### 6. 前端权限控制实现

#### 在用户管理页面中的应用

**按钮权限控制**
```vue
<template>
  <!-- 新增按钮 - 需要新增权限 -->
  <NButton v-if="hasPermission('system:user:add')" type="primary" @click="handleAdd">
    新增用户
  </NButton>
  
  <!-- 批量删除按钮 - 需要删除权限 -->
  <NButton 
    v-if="hasPermission('system:user:delete')" 
    type="error" 
    :disabled="selectedUserIds.length === 0" 
    @click="handleBatchDelete"
  >
    批量删除
  </NButton>
</template>
```

**表格操作列权限控制**
```typescript
render: (row: UserData) => {
  const buttons = [];
  
  // 编辑按钮 - 需要编辑权限
  if (hasPermission('system:user:edit')) {
    buttons.push(
      h(NButton, {
        size: 'small',
        type: 'primary',
        onClick: () => handleEdit(row)
      }, { default: () => '编辑' })
    );
  }
  
  // 删除按钮 - 需要删除权限
  if (hasPermission('system:user:delete')) {
    buttons.push(
      h(NPopconfirm, {
        onPositiveClick: () => handleDelete(row.id!)
      }, {
        default: () => '确定删除此用户吗？',
        trigger: () => h(NButton, {
          size: 'small',
          type: 'error'
        }, { default: () => '删除' })
      })
    );
  }
  
  return h('div', { class: 'flex gap-8px flex-wrap' }, buttons);
}
```

## 权限体系设计

### 权限标识规范

采用模块:功能:操作的三级权限标识：

```
system:user:list    - 查看用户列表
system:user:add     - 新增用户
system:user:edit    - 编辑用户
system:user:delete  - 删除用户

system:menu:list    - 查看菜单列表
system:menu:add     - 新增菜单
system:menu:edit    - 编辑菜单
system:menu:delete  - 删除菜单

order:list          - 查看订单列表
order:view          - 查看订单详情
order:edit          - 编辑订单
order:delete        - 删除订单
order:create        - 创建订单

purchase:task:list  - 查看采购任务
purchase:record:list - 查看采购记录
```

### 菜单类型说明

- **type: 1** - 菜单类型，有路由路径，可以导航
- **type: 2** - 按钮类型，无路由路径，用于权限控制

### 权限层级结构

```
系统管理 (system)
├── 用户管理 (system:user:list)
│   ├── 新增用户 (system:user:add)
│   ├── 编辑用户 (system:user:edit)
│   └── 删除用户 (system:user:delete)
└── 菜单管理 (system:menu:list)
    ├── 新增菜单 (system:menu:add)
    ├── 编辑菜单 (system:menu:edit)
    └── 删除菜单 (system:menu:delete)

订单管理 (order)
├── 订单列表 (order:list)
│   ├── 查看订单 (order:view)
│   ├── 编辑订单 (order:edit)
│   └── 删除订单 (order:delete)
└── 创建订单 (order:create)

采购管理 (purchase)
├── 采购任务 (purchase:task:list)
└── 采购记录 (purchase:record:list)
```

## 使用指南

### 1. 初始化权限

在应用启动时获取用户权限：

```typescript
import { getUserMenuPermissions } from '@/utils/permission';

// 在登录成功后或应用初始化时调用
await getUserMenuPermissions();
```

### 2. 在组件中使用权限

```vue
<script setup lang="ts">
import { useAuth } from '@/hooks/useAuth';

const { hasPermission, authState } = useAuth();

// 检查权限
const canAddUser = hasPermission('system:user:add');
const canEditUser = hasPermission('system:user:edit');
</script>

<template>
  <div>
    <!-- 使用v-if进行权限控制 -->
    <NButton v-if="hasPermission('system:user:add')" @click="handleAdd">
      新增用户
    </NButton>
    
    <!-- 使用权限指令 -->
    <NButton v-permission="'system:user:edit'" @click="handleEdit">
      编辑用户
    </NButton>
    
    <!-- 多权限控制 -->
    <NButton v-permission="['system:user:add', 'system:user:edit']" @click="handleAction">
      操作按钮
    </NButton>
  </div>
</template>
```

### 3. 路由权限控制

```typescript
import { canAccessRoute } from '@/hooks/useAuth';

// 检查是否可以访问某个路由
if (canAccessRoute('/system/user')) {
  // 可以访问用户管理页面
}
```

## 项目状态

- ✅ **API接口对接完成**: 获取用户菜单权限接口
- ✅ **权限管理工具**: 完整的权限检查和管理功能
- ✅ **Vue指令**: v-permission指令用于模板权限控制
- ✅ **组合式函数**: useAuth Hook便于组件使用
- ✅ **实际应用**: 在用户管理页面中应用权限控制
- ✅ **类型安全**: 完整的TypeScript类型定义

## 接口测试

可以使用以下curl命令测试接口：

```bash
# 获取用户ID为4的菜单权限
curl --location --request GET 'http://************:8080/api/menus/user/4' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'
```

现在权限管理系统已完全集成到项目中，支持细粒度的权限控制，确保用户只能访问和操作有权限的功能。
