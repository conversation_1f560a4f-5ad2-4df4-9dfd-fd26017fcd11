# CDN上传功能适配文档

## 概述

本文档记录了将CDN上传功能适配到当前项目的过程，包括API接口定义、工具函数重构和项目架构适配。

## 修改内容

### 1. 新增API服务 (src/service/api/upload.ts)

创建了专门的上传API服务文件，包含两个核心函数：

#### getSign - 获取CDN上传签名
```typescript
export function getSign(params: {
  method: string;
  save_key: string;
  status: number;
}) {
  return request<{
    authorization: string;
    policy: string;
    bucket: string;
  }>({
    url: '/v1/cdn/token',
    method: 'get',
    params
  });
}
```

#### uploadToCDN - 上传文件到CDN
```typescript
export function uploadToCDN(
  file: File,
  bucket: string,
  authorization: string,
  policy: string,
  onProgress?: (progress: number) => void
) {
  const formData = new FormData();
  formData.append('authorization', authorization);
  formData.append('policy', policy);
  formData.append('file', file);

  return request<any>({
    url: `/${bucket}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted);
      }
    }
  });
}
```

### 2. 重构工具函数 (src/utils/common.ts)

#### getSecret - 获取上传签名
**修改前**：使用未定义的GetSign函数和复杂的Promise处理
**修改后**：使用项目标准的API调用方式

```typescript
async function getSecret(scene: string, name: string = '', status = UploadWay.TEST) {
  try {
    const SAVE_PATH = `/${scene}/{filemd5}/${name}`;

    const params = {
      method: 'post',
      save_key: SAVE_PATH,
      status,
    };

    // 获取签名
    const { data, error } = await getSign(params);
    
    if (!error && data) {
      return data;
    } else {
      throw new Error('获取签名失败');
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '获取签名失败');
  }
}
```

#### uploadCDNImg - 上传文件到CDN
**修改前**：使用未定义的api函数、ElMessage和环境变量
**修改后**：使用项目标准的API调用和消息提示

```typescript
export async function uploadCDNImg(
  file: File,
  scene: string = 'product',
  name: string = '',
  status = UploadWay.TEST,
  onProgress?: (progress: number) => void,
): Promise<any> {
  try {
    // 获取签名信息
    const result = await getSecret(scene, name, status);
    
    if (!result) {
      throw new Error('获取签名失败');
    }

    // 显示上传提示
    window.$message?.info('上传中...');

    // 上传文件到CDN
    const { data, error } = await uploadToCDN(
      file,
      result.bucket,
      result.authorization,
      result.policy,
      onProgress
    );

    if (!error && data) {
      window.$message?.success('上传成功');
      return data;
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    window.$message?.error(errorMessage);
    throw new Error(errorMessage);
  }
}
```

### 3. 更新API导出 (src/service/api/index.ts)

添加了upload模块的导出：
```typescript
export * from './upload';
```

## 主要改进

### 1. 架构适配
- **统一API调用方式**：使用项目标准的request函数
- **统一错误处理**：使用项目的错误处理模式
- **统一消息提示**：使用window.$message替代ElMessage

### 2. 代码质量提升
- **TypeScript类型安全**：添加完整的类型定义
- **异步处理优化**：从Promise改为async/await
- **错误处理完善**：添加详细的错误捕获和处理

### 3. 功能保持
- **上传进度回调**：保持原有的进度监听功能
- **场景分类**：保持scene参数用于文件分类
- **环境区分**：保持status参数用于测试/正式环境区分

## 使用方式

### 基本用法
```typescript
import { uploadCDNImg, UploadWay } from '@/utils/common';

// 上传图片到CDN
const uploadImage = async (file: File) => {
  try {
    const result = await uploadCDNImg(
      file,
      'product',        // 场景值
      'image.jpg',      // 文件名
      UploadWay.TEST,   // 环境：测试
      (progress) => {   // 进度回调
        console.log(`上传进度: ${progress}%`);
      }
    );
    console.log('上传成功:', result);
  } catch (error) {
    console.error('上传失败:', error);
  }
};
```

### 在组件中使用
```typescript
// 在Vue组件中使用
const handleFileUpload = async (file: File) => {
  try {
    const cdnUrl = await uploadCDNImg(file, 'order', '', UploadWay.PROD);
    // 使用CDN URL
    formData.value.imageUrl = cdnUrl;
  } catch (error) {
    // 错误已经通过window.$message显示给用户
  }
};
```

## 接口说明

### /v1/cdn/token (GET)
- **功能**：获取CDN上传签名
- **参数**：
  - method: 上传方法
  - save_key: 保存路径
  - status: 环境状态（1=测试，2=正式）
- **返回**：
  - authorization: 授权信息
  - policy: 策略信息
  - bucket: 存储桶名称

## 注意事项

1. **环境配置**：确保后端CDN token接口已正确配置
2. **权限验证**：上传接口需要正确的Authorization头
3. **文件大小**：注意CDN服务商的文件大小限制
4. **错误处理**：所有错误都会通过window.$message显示给用户
5. **进度监听**：可选的进度回调函数用于显示上传进度

## 兼容性

- ✅ 保持原有API接口不变
- ✅ 保持原有函数签名不变
- ✅ 保持原有功能特性不变
- ✅ 适配项目架构和代码规范
- ✅ 提升TypeScript类型安全性
